/* Light Theme */
.root {
    -fx-font-family: "Segoe UI", Arial, sans-serif;
    -fx-background-color: #f5f5f5;
    -fx-text-fill: #333333;
}

/* Menu bar styles */
.menu-bar {
    -fx-background-color: #e0e0e0;
}

.menu-bar .label {
    -fx-text-fill: #333333;
}

.menu-item {
    -fx-background-color: #f5f5f5;
}

.menu-item .label {
    -fx-text-fill: #333333;
}

.menu-item:hover {
    -fx-background-color: #e0e0e0;
}

/* Button styles */
.button {
    -fx-background-color: #e0e0e0;
    -fx-text-fill: #333333;
    -fx-background-radius: 3;
}

.button:hover {
    -fx-background-color: #d0d0d0;
}

.button:pressed {
    -fx-background-color: #c0c0c0;
}

/* Transport control buttons */
#transportControls .button {
    -fx-font-size: 16px;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-background-radius: 20;
}

#playButton {
    -fx-background-color: #1db954;
    -fx-text-fill: white;
}

#playButton:hover {
    -fx-background-color: #1ed760;
}

/* Slider styles */
.slider .track {
    -fx-background-color: #c0c0c0;
}

.slider .thumb {
    -fx-background-color: #1db954;
}

/* List view styles */
.list-view {
    -fx-background-color: #f5f5f5;
    -fx-control-inner-background: #f5f5f5;
}

.list-cell {
    -fx-background-color: #f5f5f5;
    -fx-text-fill: #333333;
}

.list-cell:filled:selected {
    -fx-background-color: #1db954;
    -fx-text-fill: white;
}

.list-cell:filled:hover {
    -fx-background-color: #e0e0e0;
}

/* Tab pane styles */
.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: #e0e0e0;
}

.tab-pane .tab {
    -fx-background-color: #f5f5f5;
}

.tab-pane .tab:selected {
    -fx-background-color: #d0d0d0;
}

.tab .tab-label {
    -fx-text-fill: #333333;
}

/* Equalizer styles */
.equalizer-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.freq-label {
    -fx-text-fill: #333333;
    -fx-font-size: 10px;
}

.value-label {
    -fx-text-fill: #333333;
    -fx-font-size: 10px;
}

.preset-button {
    -fx-background-color: #e0e0e0;
    -fx-text-fill: #333333;
    -fx-background-radius: 3;
    -fx-font-size: 11px;
}

.preset-button:hover {
    -fx-background-color: #d0d0d0;
}

/* Sound effects styles */
.effects-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.effect-button {
    -fx-background-color: #e0e0e0;
    -fx-text-fill: #333333;
    -fx-background-radius: 3;
    -fx-font-size: 11px;
}

.effect-button:hover {
    -fx-background-color: #d0d0d0;
}

/* Split pane styles */
.split-pane {
    -fx-background-color: #f5f5f5;
}

.split-pane-divider {
    -fx-background-color: #e0e0e0;
    -fx-padding: 0 1 0 1;
}

/* Label styles */
.label {
    -fx-text-fill: #333333;
}

/* Check box styles */
.check-box {
    -fx-text-fill: #333333;
}

.check-box .box {
    -fx-background-color: #e0e0e0;
}

.check-box:selected .mark {
    -fx-background-color: white;
}

.check-box:selected .box {
    -fx-background-color: #1db954;
}

/* Dialog styles */
.dialog-pane {
    -fx-background-color: #f5f5f5;
}

.dialog-pane .label {
    -fx-text-fill: #333333;
}

.dialog-pane:header .header-panel {
    -fx-background-color: #e0e0e0;
}

.dialog-pane:header .header-panel .label {
    -fx-text-fill: #333333;
}

/* Text field styles */
.text-field {
    -fx-background-color: white;
    -fx-text-fill: #333333;
    -fx-prompt-text-fill: #a0a0a0;
}

/* Combo box styles */
.combo-box {
    -fx-background-color: #e0e0e0;
    -fx-text-fill: #333333;
}

.combo-box .list-cell {
    -fx-text-fill: #333333;
}

.combo-box-popup .list-view {
    -fx-background-color: white;
}

/* Scroll bar styles */
.scroll-bar {
    -fx-background-color: #f5f5f5;
}

.scroll-bar .thumb {
    -fx-background-color: #c0c0c0;
}

.scroll-bar .increment-button,
.scroll-bar .decrement-button {
    -fx-background-color: #e0e0e0;
}

/* Table view styles */
.table-view {
    -fx-background-color: #f5f5f5;
    -fx-table-cell-border-color: #e0e0e0;
}

.table-view .column-header-background {
    -fx-background-color: #e0e0e0;
}

.table-view .column-header, .table-view .filler {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.table-view .column-header .label {
    -fx-text-fill: #333333;
}

.table-row-cell {
    -fx-background-color: #f5f5f5;
    -fx-text-fill: #333333;
}

.table-row-cell:selected {
    -fx-background-color: #1db954;
    -fx-text-fill: white;
}

/* Visualization styles */
.visualization-container {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1;
}

/* Tooltip styles */
.tooltip {
    -fx-background-color: #e0e0e0;
    -fx-text-fill: #333333;
}

/* Progress bar styles */
.progress-bar {
    -fx-background-color: #e0e0e0;
}

.progress-bar .bar {
    -fx-background-color: #1db954;
}

/* Separator styles */
.separator .line {
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1;
}
