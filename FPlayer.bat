@echo off
REM FPlayer - Media Player Application
REM Windows Batch File Launcher

echo Starting FPlayer...
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java 11 or higher to run FPlayer
    pause
    exit /b 1
)

REM Check if Maven is available
where mvn >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Maven to run FPlayer...
    mvn clean javafx:run
) else (
    REM Try to run with compiled JAR
    if exist "target\FPlayer-1.0-SNAPSHOT.jar" (
        echo Running FPlayer from JAR...
        java -jar target\FPlayer-1.0-SNAPSHOT.jar
    ) else (
        echo Error: Neither Maven nor compiled JAR found
        echo Please compile the project first using: mvn clean compile
        pause
        exit /b 1
    )
)

if %errorlevel% neq 0 (
    echo.
    echo FPlayer encountered an error during execution
    pause
)
