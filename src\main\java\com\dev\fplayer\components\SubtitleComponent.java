package com.dev.fplayer.components;

import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.layout.StackPane;
import javafx.scene.media.MediaPlayer;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.scene.text.TextAlignment;
import javafx.util.Duration;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Component for displaying subtitles during video playback
 */
public class SubtitleComponent extends StackPane {
    
    private MediaPlayer mediaPlayer;
    private Label subtitleLabel;
    private List<Subtitle> subtitles;
    private Timeline checkTimeline;
    private ObjectProperty<File> subtitleFile;
    private ObjectProperty<Color> textColor;
    private ObjectProperty<Color> outlineColor;
    private ObjectProperty<Double> fontSize;
    
    /**
     * Create a new subtitle component
     */
    public SubtitleComponent() {
        subtitles = new ArrayList<>();
        subtitleFile = new SimpleObjectProperty<>();
        textColor = new SimpleObjectProperty<>(Color.WHITE);
        outlineColor = new SimpleObjectProperty<>(Color.BLACK);
        fontSize = new SimpleObjectProperty<>(24.0);
        
        // Create subtitle label
        subtitleLabel = new Label();
        subtitleLabel.setAlignment(Pos.CENTER);
        subtitleLabel.setTextAlignment(TextAlignment.CENTER);
        subtitleLabel.setWrapText(true);
        subtitleLabel.setMaxWidth(800);
        subtitleLabel.setStyle(
            "-fx-text-fill: white; " +
            "-fx-effect: dropshadow(gaussian, black, 2, 0.5, 0, 0);"
        );
        
        // Bind style properties
        textColor.addListener((obs, oldVal, newVal) -> updateLabelStyle());
        outlineColor.addListener((obs, oldVal, newVal) -> updateLabelStyle());
        fontSize.addListener((obs, oldVal, newVal) -> updateLabelStyle());
        
        // Set initial style
        updateLabelStyle();
        
        // Add label to component
        setAlignment(Pos.BOTTOM_CENTER);
        getChildren().add(subtitleLabel);
        
        // Create timeline for checking subtitles
        checkTimeline = new Timeline(new KeyFrame(Duration.millis(100), e -> checkSubtitles()));
        checkTimeline.setCycleCount(Timeline.INDEFINITE);
        
        // Listen for subtitle file changes
        subtitleFile.addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                loadSubtitles(newVal);
            } else {
                subtitles.clear();
                subtitleLabel.setText("");
            }
        });
    }
    
    /**
     * Update the label style based on properties
     */
    private void updateLabelStyle() {
        String textColorHex = String.format("#%02X%02X%02X", 
            (int)(textColor.get().getRed() * 255),
            (int)(textColor.get().getGreen() * 255),
            (int)(textColor.get().getBlue() * 255));
        
        String outlineColorHex = String.format("#%02X%02X%02X", 
            (int)(outlineColor.get().getRed() * 255),
            (int)(outlineColor.get().getGreen() * 255),
            (int)(outlineColor.get().getBlue() * 255));
        
        subtitleLabel.setStyle(
            "-fx-text-fill: " + textColorHex + "; " +
            "-fx-effect: dropshadow(gaussian, " + outlineColorHex + ", 2, 0.5, 0, 0);" +
            "-fx-font-size: " + fontSize.get() + "px;" +
            "-fx-font-weight: bold;"
        );
        
        subtitleLabel.setFont(Font.font("Arial", FontWeight.BOLD, fontSize.get()));
    }
    
    /**
     * Connect to a media player
     * @param mediaPlayer The media player to connect to
     */
    public void connectToMediaPlayer(MediaPlayer mediaPlayer) {
        // Disconnect from previous media player if any
        if (this.mediaPlayer != null) {
            checkTimeline.stop();
        }
        
        this.mediaPlayer = mediaPlayer;
        
        if (mediaPlayer != null) {
            // Start checking for subtitles
            checkTimeline.play();
            
            // Try to load subtitles with the same name as the media
            if (mediaPlayer.getMedia().getSource().startsWith("file:")) {
                String mediaPath = mediaPlayer.getMedia().getSource().substring(5);
                String basePath = mediaPath.substring(0, mediaPath.lastIndexOf('.'));
                
                // Try common subtitle extensions
                for (String ext : new String[]{".srt", ".vtt", ".sub"}) {
                    File subFile = new File(basePath + ext);
                    if (subFile.exists()) {
                        subtitleFile.set(subFile);
                        break;
                    }
                }
            }
        }
    }
    
    /**
     * Check for subtitles at the current playback time
     */
    private void checkSubtitles() {
        if (mediaPlayer == null || subtitles.isEmpty()) {
            return;
        }
        
        Duration currentTime = mediaPlayer.getCurrentTime();
        String text = "";
        
        for (Subtitle subtitle : subtitles) {
            if (currentTime.greaterThanOrEqualTo(subtitle.startTime) && 
                currentTime.lessThan(subtitle.endTime)) {
                text = subtitle.text;
                break;
            }
        }
        
        subtitleLabel.setText(text);
    }
    
    /**
     * Load subtitles from a file
     * @param file The subtitle file
     */
    public void loadSubtitles(File file) {
        subtitles.clear();
        
        if (file == null || !file.exists()) {
            return;
        }
        
        String extension = file.getName().substring(file.getName().lastIndexOf('.') + 1).toLowerCase();
        
        try {
            switch (extension) {
                case "srt":
                    loadSrtSubtitles(file);
                    break;
                case "vtt":
                    loadVttSubtitles(file);
                    break;
                case "sub":
                    loadSubSubtitles(file);
                    break;
                default:
                    System.err.println("Unsupported subtitle format: " + extension);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Load subtitles in SRT format
     * @param file The SRT file
     * @throws IOException If an I/O error occurs
     */
    private void loadSrtSubtitles(File file) throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int state = 0; // 0 = index, 1 = time, 2 = text
            int index = 0;
            Duration startTime = Duration.ZERO;
            Duration endTime = Duration.ZERO;
            StringBuilder text = new StringBuilder();
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                if (line.isEmpty()) {
                    if (state == 2 && !text.toString().isEmpty()) {
                        subtitles.add(new Subtitle(index, startTime, endTime, text.toString()));
                    }
                    state = 0;
                    text = new StringBuilder();
                    continue;
                }
                
                switch (state) {
                    case 0: // Index
                        try {
                            index = Integer.parseInt(line);
                            state = 1;
                        } catch (NumberFormatException e) {
                            // Not a valid index, try to continue
                            state = 1;
                        }
                        break;
                    
                    case 1: // Time
                        Pattern timePattern = Pattern.compile("(\\d{2}):(\\d{2}):(\\d{2}),(\\d{3}) --> (\\d{2}):(\\d{2}):(\\d{2}),(\\d{3})");
                        Matcher matcher = timePattern.matcher(line);
                        
                        if (matcher.find()) {
                            int startHours = Integer.parseInt(matcher.group(1));
                            int startMinutes = Integer.parseInt(matcher.group(2));
                            int startSeconds = Integer.parseInt(matcher.group(3));
                            int startMillis = Integer.parseInt(matcher.group(4));
                            
                            int endHours = Integer.parseInt(matcher.group(5));
                            int endMinutes = Integer.parseInt(matcher.group(6));
                            int endSeconds = Integer.parseInt(matcher.group(7));
                            int endMillis = Integer.parseInt(matcher.group(8));
                            
                            startTime = Duration.hours(startHours).add(Duration.minutes(startMinutes))
                                .add(Duration.seconds(startSeconds)).add(Duration.millis(startMillis));
                            
                            endTime = Duration.hours(endHours).add(Duration.minutes(endMinutes))
                                .add(Duration.seconds(endSeconds)).add(Duration.millis(endMillis));
                            
                            state = 2;
                        }
                        break;
                    
                    case 2: // Text
                        if (text.length() > 0) {
                            text.append("\n");
                        }
                        text.append(line);
                        break;
                }
            }
            
            // Add the last subtitle if any
            if (state == 2 && !text.toString().isEmpty()) {
                subtitles.add(new Subtitle(index, startTime, endTime, text.toString()));
            }
        }
    }
    
    /**
     * Load subtitles in WebVTT format
     * @param file The VTT file
     * @throws IOException If an I/O error occurs
     */
    private void loadVttSubtitles(File file) throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int state = 0; // 0 = header, 1 = index, 2 = time, 3 = text
            int index = 0;
            Duration startTime = Duration.ZERO;
            Duration endTime = Duration.ZERO;
            StringBuilder text = new StringBuilder();
            
            // Skip the WebVTT header
            line = reader.readLine();
            if (line == null || !line.startsWith("WEBVTT")) {
                throw new IOException("Invalid WebVTT file");
            }
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                if (line.isEmpty()) {
                    if (state == 3 && !text.toString().isEmpty()) {
                        subtitles.add(new Subtitle(index, startTime, endTime, text.toString()));
                    }
                    state = 1;
                    text = new StringBuilder();
                    continue;
                }
                
                switch (state) {
                    case 0: // Header
                        // Skip header lines
                        break;
                    
                    case 1: // Index or note
                        // Could be an index or a note, move to time state
                        state = 2;
                        try {
                            index = Integer.parseInt(line);
                        } catch (NumberFormatException e) {
                            // Not a valid index, might be a note or already a time
                            if (line.contains("-->")) {
                                state = 2;
                                // Process as time
                                processVttTime(line, startTime, endTime);
                                state = 3;
                            }
                        }
                        break;
                    
                    case 2: // Time
                        if (line.contains("-->")) {
                            processVttTime(line, startTime, endTime);
                            state = 3;
                        }
                        break;
                    
                    case 3: // Text
                        if (text.length() > 0) {
                            text.append("\n");
                        }
                        text.append(line);
                        break;
                }
            }
            
            // Add the last subtitle if any
            if (state == 3 && !text.toString().isEmpty()) {
                subtitles.add(new Subtitle(index, startTime, endTime, text.toString()));
            }
        }
    }
    
    /**
     * Process a WebVTT time line
     * @param line The time line
     * @param startTime The start time to set
     * @param endTime The end time to set
     */
    private void processVttTime(String line, Duration startTime, Duration endTime) {
        Pattern timePattern = Pattern.compile("(\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{3}) --> (\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{3})");
        Matcher matcher = timePattern.matcher(line);
        
        if (matcher.find()) {
            int startHours = Integer.parseInt(matcher.group(1));
            int startMinutes = Integer.parseInt(matcher.group(2));
            int startSeconds = Integer.parseInt(matcher.group(3));
            int startMillis = Integer.parseInt(matcher.group(4));
            
            int endHours = Integer.parseInt(matcher.group(5));
            int endMinutes = Integer.parseInt(matcher.group(6));
            int endSeconds = Integer.parseInt(matcher.group(7));
            int endMillis = Integer.parseInt(matcher.group(8));
            
            startTime = Duration.hours(startHours).add(Duration.minutes(startMinutes))
                .add(Duration.seconds(startSeconds)).add(Duration.millis(startMillis));
            
            endTime = Duration.hours(endHours).add(Duration.minutes(endMinutes))
                .add(Duration.seconds(endSeconds)).add(Duration.millis(endMillis));
        }
    }
    
    /**
     * Load subtitles in SUB format (MicroDVD)
     * @param file The SUB file
     * @throws IOException If an I/O error occurs
     */
    private void loadSubSubtitles(File file) throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int index = 0;
            Pattern pattern = Pattern.compile("\\{(\\d+)\\}\\{(\\d+)\\}(.*)");
            
            while ((line = reader.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                
                if (matcher.find()) {
                    int startFrame = Integer.parseInt(matcher.group(1));
                    int endFrame = Integer.parseInt(matcher.group(2));
                    String text = matcher.group(3);
                    
                    // Convert frames to time (assuming 25 fps)
                    Duration startTime = Duration.seconds(startFrame / 25.0);
                    Duration endTime = Duration.seconds(endFrame / 25.0);
                    
                    // Clean up text (replace | with newline)
                    text = text.replace("|", "\n");
                    
                    subtitles.add(new Subtitle(index++, startTime, endTime, text));
                }
            }
        }
    }
    
    /**
     * Get the subtitle file property
     * @return The subtitle file property
     */
    public ObjectProperty<File> subtitleFileProperty() {
        return subtitleFile;
    }
    
    /**
     * Set the subtitle file
     * @param file The subtitle file
     */
    public void setSubtitleFile(File file) {
        subtitleFile.set(file);
    }
    
    /**
     * Get the subtitle file
     * @return The subtitle file
     */
    public File getSubtitleFile() {
        return subtitleFile.get();
    }
    
    /**
     * Get the text color property
     * @return The text color property
     */
    public ObjectProperty<Color> textColorProperty() {
        return textColor;
    }
    
    /**
     * Set the text color
     * @param color The text color
     */
    public void setTextColor(Color color) {
        textColor.set(color);
    }
    
    /**
     * Get the text color
     * @return The text color
     */
    public Color getTextColor() {
        return textColor.get();
    }
    
    /**
     * Get the outline color property
     * @return The outline color property
     */
    public ObjectProperty<Color> outlineColorProperty() {
        return outlineColor;
    }
    
    /**
     * Set the outline color
     * @param color The outline color
     */
    public void setOutlineColor(Color color) {
        outlineColor.set(color);
    }
    
    /**
     * Get the outline color
     * @return The outline color
     */
    public Color getOutlineColor() {
        return outlineColor.get();
    }
    
    /**
     * Get the font size property
     * @return The font size property
     */
    public ObjectProperty<Double> fontSizeProperty() {
        return fontSize;
    }
    
    /**
     * Set the font size
     * @param size The font size
     */
    public void setFontSize(double size) {
        fontSize.set(size);
    }
    
    /**
     * Get the font size
     * @return The font size
     */
    public double getFontSize() {
        return fontSize.get();
    }
    
    /**
     * Class representing a subtitle entry
     */
    private static class Subtitle {
        private int index;
        private Duration startTime;
        private Duration endTime;
        private String text;
        
        /**
         * Create a new subtitle
         * @param index The subtitle index
         * @param startTime The start time
         * @param endTime The end time
         * @param text The subtitle text
         */
        public Subtitle(int index, Duration startTime, Duration endTime, String text) {
            this.index = index;
            this.startTime = startTime;
            this.endTime = endTime;
            this.text = text;
        }
    }
}
