package com.dev.fplayer;

import com.dev.fplayer.utils.ThemeManager;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.application.Preloader.ProgressNotification;
import javafx.concurrent.Task;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;

import java.io.IOException;

/**
 * FPlayer - A comprehensive media player application
 */
public class App extends Application {

    private static Scene scene;

    @Override
    public void init() throws Exception {
        // Simulate loading tasks for the splash screen
        for (int i = 0; i < 10; i++) {
            double progress = (i + 1) / 10.0;
            notifyPreloader(new ProgressNotification(progress));
            Thread.sleep(200); // Simulate loading time
        }
    }

    @Override
    public void start(Stage stage) throws IOException {
        // Load the main FXML
        scene = new Scene(loadFXML("views/main_player"), 900, 600);
        scene.getStylesheets().add(getClass().getResource("css/player.css").toExternalForm());

        // Initialize theme manager
        ThemeManager themeManager = ThemeManager.getInstance();
        themeManager.initialize(scene, "Dark");

        // Set application icon
        stage.getIcons().add(new Image(getClass().getResourceAsStream("/com/dev/fplayer/assets/app-icon.png")));

        // Configure the stage
        stage.setTitle("FPlayer - Comprehensive Media Player");
        stage.setScene(scene);
        stage.setMinWidth(800);
        stage.setMinHeight(500);

        // Show the main window
        stage.show();

        // Initialize components in background
        Platform.runLater(() -> {
            // Additional initialization that can happen after the UI is shown
            System.out.println("Application fully loaded");
        });
    }

    static void setRoot(String fxml) throws IOException {
        scene.setRoot(loadFXML(fxml));
    }

    private static Parent loadFXML(String fxml) throws IOException {
        FXMLLoader fxmlLoader = new FXMLLoader(App.class.getResource(fxml + ".fxml"));
        return fxmlLoader.load();
    }

    public static void main(String[] args) {
        // Launch with splash screen
        System.setProperty("javafx.preloader", SplashScreen.class.getName());
        launch(args);
    }

}