#!/bin/bash
# FPlayer - Media Player Application
# Linux/macOS Shell Script Launcher

echo "Starting FPlayer..."
echo

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if Java is installed
if ! command_exists java; then
    echo "Error: Java is not installed or not in PATH"
    echo "Please install Java 11 or higher to run FPlayer"
    exit 1
fi

# Display Java version
echo "Java version:"
java -version
echo

# Check if <PERSON><PERSON> is available
if command_exists mvn; then
    echo "Using <PERSON><PERSON> to run FPlayer..."
    mvn clean javafx:run
    exit_code=$?
else
    # Try to run with compiled JAR
    if [ -f "target/FPlayer-1.0-SNAPSHOT.jar" ]; then
        echo "Running FPlayer from JAR..."
        java -jar target/FPlayer-1.0-SNAPSHOT.jar
        exit_code=$?
    else
        echo "Error: Neither <PERSON>ven nor compiled J<PERSON> found"
        echo "Please compile the project first using: mvn clean compile"
        exit 1
    fi
fi

if [ $exit_code -ne 0 ]; then
    echo
    echo "FPlayer encountered an error during execution"
    exit $exit_code
fi

echo "FPlayer finished successfully"
