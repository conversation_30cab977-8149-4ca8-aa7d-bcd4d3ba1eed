package com.dev.fplayer.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javafx.util.Duration;

/**
 * Utility class for parsing subtitle files
 */
public class SubtitleParser {
    
    /**
     * Represents a subtitle entry with timing and text
     */
    public static class SubtitleEntry {
        private Duration startTime;
        private Duration endTime;
        private String text;
        
        public SubtitleEntry(Duration startTime, Duration endTime, String text) {
            this.startTime = startTime;
            this.endTime = endTime;
            this.text = text;
        }
        
        public Duration getStartTime() {
            return startTime;
        }
        
        public Duration getEndTime() {
            return endTime;
        }
        
        public String getText() {
            return text;
        }
        
        public boolean isActiveAt(Duration time) {
            return time.greaterThanOrEqualTo(startTime) && time.lessThan(endTime);
        }
    }
    
    /**
     * Parse a SubRip (SRT) subtitle file
     * @param file The SRT file to parse
     * @return A list of subtitle entries
     * @throws IOException If the file cannot be read
     */
    public static List<SubtitleEntry> parseSRT(File file) throws IOException {
        List<SubtitleEntry> entries = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int state = 0; // 0 = index, 1 = timing, 2 = text
            
            Duration startTime = null;
            Duration endTime = null;
            StringBuilder textBuilder = new StringBuilder();
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                if (line.isEmpty()) {
                    // End of entry
                    if (startTime != null && endTime != null && textBuilder.length() > 0) {
                        entries.add(new SubtitleEntry(startTime, endTime, textBuilder.toString().trim()));
                    }
                    
                    // Reset for next entry
                    startTime = null;
                    endTime = null;
                    textBuilder.setLength(0);
                    state = 0;
                    continue;
                }
                
                switch (state) {
                    case 0: // Index
                        // Just skip the index number
                        state = 1;
                        break;
                        
                    case 1: // Timing
                        // Parse timing line like "00:00:20,000 --> 00:00:24,400"
                        Pattern pattern = Pattern.compile("(\\d{2}):(\\d{2}):(\\d{2}),(\\d{3}) --> (\\d{2}):(\\d{2}):(\\d{2}),(\\d{3})");
                        Matcher matcher = pattern.matcher(line);
                        
                        if (matcher.find()) {
                            int startHours = Integer.parseInt(matcher.group(1));
                            int startMinutes = Integer.parseInt(matcher.group(2));
                            int startSeconds = Integer.parseInt(matcher.group(3));
                            int startMillis = Integer.parseInt(matcher.group(4));
                            
                            int endHours = Integer.parseInt(matcher.group(5));
                            int endMinutes = Integer.parseInt(matcher.group(6));
                            int endSeconds = Integer.parseInt(matcher.group(7));
                            int endMillis = Integer.parseInt(matcher.group(8));
                            
                            startTime = Duration.hours(startHours).add(Duration.minutes(startMinutes))
                                    .add(Duration.seconds(startSeconds)).add(Duration.millis(startMillis));
                            
                            endTime = Duration.hours(endHours).add(Duration.minutes(endMinutes))
                                    .add(Duration.seconds(endSeconds)).add(Duration.millis(endMillis));
                            
                            state = 2;
                        }
                        break;
                        
                    case 2: // Text
                        if (textBuilder.length() > 0) {
                            textBuilder.append("\n");
                        }
                        textBuilder.append(line);
                        break;
                }
            }
            
            // Add the last entry if there is one
            if (startTime != null && endTime != null && textBuilder.length() > 0) {
                entries.add(new SubtitleEntry(startTime, endTime, textBuilder.toString().trim()));
            }
        }
        
        return entries;
    }
    
    /**
     * Parse a WebVTT subtitle file
     * @param file The WebVTT file to parse
     * @return A list of subtitle entries
     * @throws IOException If the file cannot be read
     */
    public static List<SubtitleEntry> parseVTT(File file) throws IOException {
        List<SubtitleEntry> entries = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            boolean headerPassed = false;
            int state = 0; // 0 = cue identifier, 1 = timing, 2 = text
            
            Duration startTime = null;
            Duration endTime = null;
            StringBuilder textBuilder = new StringBuilder();
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                // Skip the WebVTT header
                if (!headerPassed) {
                    if (line.startsWith("WEBVTT")) {
                        headerPassed = true;
                    }
                    continue;
                }
                
                if (line.isEmpty()) {
                    // End of entry
                    if (startTime != null && endTime != null && textBuilder.length() > 0) {
                        entries.add(new SubtitleEntry(startTime, endTime, textBuilder.toString().trim()));
                    }
                    
                    // Reset for next entry
                    startTime = null;
                    endTime = null;
                    textBuilder.setLength(0);
                    state = 0;
                    continue;
                }
                
                switch (state) {
                    case 0: // Cue identifier
                        // Check if this is a timing line
                        if (line.contains("-->")) {
                            state = 1;
                            // Process as timing line
                            Pattern pattern = Pattern.compile("(\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{3}) --> (\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{3})");
                            Matcher matcher = pattern.matcher(line);
                            
                            if (matcher.find()) {
                                int startHours = Integer.parseInt(matcher.group(1));
                                int startMinutes = Integer.parseInt(matcher.group(2));
                                int startSeconds = Integer.parseInt(matcher.group(3));
                                int startMillis = Integer.parseInt(matcher.group(4));
                                
                                int endHours = Integer.parseInt(matcher.group(5));
                                int endMinutes = Integer.parseInt(matcher.group(6));
                                int endSeconds = Integer.parseInt(matcher.group(7));
                                int endMillis = Integer.parseInt(matcher.group(8));
                                
                                startTime = Duration.hours(startHours).add(Duration.minutes(startMinutes))
                                        .add(Duration.seconds(startSeconds)).add(Duration.millis(startMillis));
                                
                                endTime = Duration.hours(endHours).add(Duration.minutes(endMinutes))
                                        .add(Duration.seconds(endSeconds)).add(Duration.millis(endMillis));
                                
                                state = 2;
                            } else {
                                // Try alternative format (MM:SS.mmm)
                                pattern = Pattern.compile("(\\d{2}):(\\d{2})\\.(\\d{3}) --> (\\d{2}):(\\d{2})\\.(\\d{3})");
                                matcher = pattern.matcher(line);
                                
                                if (matcher.find()) {
                                    int startMinutes = Integer.parseInt(matcher.group(1));
                                    int startSeconds = Integer.parseInt(matcher.group(2));
                                    int startMillis = Integer.parseInt(matcher.group(3));
                                    
                                    int endMinutes = Integer.parseInt(matcher.group(4));
                                    int endSeconds = Integer.parseInt(matcher.group(5));
                                    int endMillis = Integer.parseInt(matcher.group(6));
                                    
                                    startTime = Duration.minutes(startMinutes)
                                            .add(Duration.seconds(startSeconds)).add(Duration.millis(startMillis));
                                    
                                    endTime = Duration.minutes(endMinutes)
                                            .add(Duration.seconds(endSeconds)).add(Duration.millis(endMillis));
                                    
                                    state = 2;
                                }
                            }
                        } else {
                            // Skip cue identifier
                            state = 1;
                        }
                        break;
                        
                    case 1: // Timing
                        // Parse timing line
                        Pattern pattern = Pattern.compile("(\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{3}) --> (\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{3})");
                        Matcher matcher = pattern.matcher(line);
                        
                        if (matcher.find()) {
                            int startHours = Integer.parseInt(matcher.group(1));
                            int startMinutes = Integer.parseInt(matcher.group(2));
                            int startSeconds = Integer.parseInt(matcher.group(3));
                            int startMillis = Integer.parseInt(matcher.group(4));
                            
                            int endHours = Integer.parseInt(matcher.group(5));
                            int endMinutes = Integer.parseInt(matcher.group(6));
                            int endSeconds = Integer.parseInt(matcher.group(7));
                            int endMillis = Integer.parseInt(matcher.group(8));
                            
                            startTime = Duration.hours(startHours).add(Duration.minutes(startMinutes))
                                    .add(Duration.seconds(startSeconds)).add(Duration.millis(startMillis));
                            
                            endTime = Duration.hours(endHours).add(Duration.minutes(endMinutes))
                                    .add(Duration.seconds(endSeconds)).add(Duration.millis(endMillis));
                            
                            state = 2;
                        } else {
                            // Try alternative format (MM:SS.mmm)
                            pattern = Pattern.compile("(\\d{2}):(\\d{2})\\.(\\d{3}) --> (\\d{2}):(\\d{2})\\.(\\d{3})");
                            matcher = pattern.matcher(line);
                            
                            if (matcher.find()) {
                                int startMinutes = Integer.parseInt(matcher.group(1));
                                int startSeconds = Integer.parseInt(matcher.group(2));
                                int startMillis = Integer.parseInt(matcher.group(3));
                                
                                int endMinutes = Integer.parseInt(matcher.group(4));
                                int endSeconds = Integer.parseInt(matcher.group(5));
                                int endMillis = Integer.parseInt(matcher.group(6));
                                
                                startTime = Duration.minutes(startMinutes)
                                        .add(Duration.seconds(startSeconds)).add(Duration.millis(startMillis));
                                
                                endTime = Duration.minutes(endMinutes)
                                        .add(Duration.seconds(endSeconds)).add(Duration.millis(endMillis));
                                
                                state = 2;
                            }
                        }
                        break;
                        
                    case 2: // Text
                        if (textBuilder.length() > 0) {
                            textBuilder.append("\n");
                        }
                        textBuilder.append(line);
                        break;
                }
            }
            
            // Add the last entry if there is one
            if (startTime != null && endTime != null && textBuilder.length() > 0) {
                entries.add(new SubtitleEntry(startTime, endTime, textBuilder.toString().trim()));
            }
        }
        
        return entries;
    }
    
    /**
     * Parse a subtitle file based on its extension
     * @param file The subtitle file to parse
     * @return A list of subtitle entries
     * @throws IOException If the file cannot be read
     * @throws UnsupportedOperationException If the file format is not supported
     */
    public static List<SubtitleEntry> parseSubtitleFile(File file) throws IOException {
        String fileName = file.getName().toLowerCase();
        
        if (fileName.endsWith(".srt")) {
            return parseSRT(file);
        } else if (fileName.endsWith(".vtt")) {
            return parseVTT(file);
        } else {
            throw new UnsupportedOperationException("Unsupported subtitle format: " + fileName);
        }
    }
}
