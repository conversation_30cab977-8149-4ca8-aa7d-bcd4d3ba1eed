/* Main application styles */
.root {
    -fx-font-family: "Segoe UI", Arial, sans-serif;
    -fx-background-color: #2b2b2b;
    -fx-text-fill: white;
    -fx-font-size: 12px;
}

/* Menu bar styles */
.menu-bar {
    -fx-background-color: #1e1e1e;
}

.menu-bar .label {
    -fx-text-fill: white;
}

.menu-item {
    -fx-background-color: #1e1e1e;
}

.menu-item .label {
    -fx-text-fill: white;
}

.menu-item:hover {
    -fx-background-color: #3c3c3c;
}

/* Button styles */
.button {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: white;
    -fx-background-radius: 3;
    -fx-padding: 5px;
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: #4c4c4c;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.6), 5, 0, 0, 0);
}

.button:pressed {
    -fx-background-color: #5c5c5c;
}

/* Transport control buttons */
#transportControls {
    -fx-background-color: rgba(30, 30, 30, 0.8);
    -fx-padding: 10px;
    -fx-background-radius: 5;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.6), 10, 0, 0, 0);
}

#transportControls .button {
    -fx-font-size: 16px;
    -fx-min-width: 50px;
    -fx-min-height: 50px;
    -fx-background-radius: 25;
    -fx-border-radius: 25;
    -fx-border-width: 1;
    -fx-border-color: #5c5c5c;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.4), 5, 0, 0, 0);
}

#playButton {
    -fx-background-color: #1db954;
    -fx-min-width: 60px;
    -fx-min-height: 60px;
    -fx-background-radius: 30;
    -fx-border-radius: 30;
}

#playButton:hover {
    -fx-background-color: #1ed760;
}

#controlsContainer {
    -fx-background-color: rgba(30, 30, 30, 0.9);
    -fx-padding: 10px;
    -fx-spacing: 10px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.8), 10, 0, 0, 0);
}

/* Slider styles */
.slider {
    -fx-pref-height: 24px;
}

.slider .track {
    -fx-background-color: #5c5c5c;
    -fx-pref-height: 8px;
    -fx-background-radius: 4;
}

.slider .thumb {
    -fx-background-color: #1db954;
    -fx-pref-height: 16px;
    -fx-pref-width: 16px;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.6), 5, 0, 0, 0);
}

.slider .thumb:hover {
    -fx-background-color: #1ed760;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.8), 8, 0, 0, 0);
}

#timeSlider .track {
    -fx-background-color: #3c3c3c;
}

#timeSlider .thumb {
    -fx-background-color: #1db954;
}

#volumeSlider .track {
    -fx-background-color: #3c3c3c;
}

#volumeSlider .thumb {
    -fx-background-color: #1db954;
}

/* List view styles */
.list-view {
    -fx-background-color: #2b2b2b;
    -fx-control-inner-background: #2b2b2b;
    -fx-border-color: #1e1e1e;
    -fx-border-width: 1px;
}

.list-cell {
    -fx-background-color: #2b2b2b;
    -fx-text-fill: white;
    -fx-padding: 5px;
}

.list-cell:filled:selected {
    -fx-background-color: #1db954;
    -fx-font-weight: bold;
}

.list-cell:filled:hover {
    -fx-background-color: #3c3c3c;
    -fx-cursor: hand;
}

/* Tab pane styles */
.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: #1e1e1e;
}

.tab-pane .tab {
    -fx-background-color: #2b2b2b;
}

.tab-pane .tab:selected {
    -fx-background-color: #3c3c3c;
}

.tab .tab-label {
    -fx-text-fill: white;
}

/* Equalizer styles */
.equalizer-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.freq-label {
    -fx-text-fill: white;
    -fx-font-size: 10px;
}

.value-label {
    -fx-text-fill: white;
    -fx-font-size: 10px;
}

.preset-button {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: white;
    -fx-background-radius: 3;
    -fx-font-size: 11px;
}

.preset-button:hover {
    -fx-background-color: #4c4c4c;
}

/* Sound effects styles */
.effects-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.effect-button {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: white;
    -fx-background-radius: 3;
    -fx-font-size: 11px;
}

.effect-button:hover {
    -fx-background-color: #4c4c4c;
}

/* Split pane styles */
.split-pane {
    -fx-background-color: #2b2b2b;
}

.split-pane-divider {
    -fx-background-color: #1e1e1e;
    -fx-padding: 0 1 0 1;
}

/* Label styles */
.label {
    -fx-text-fill: white;
}

/* Check box styles */
.check-box {
    -fx-text-fill: white;
}

.check-box .box {
    -fx-background-color: #3c3c3c;
}

.check-box:selected .mark {
    -fx-background-color: white;
}

.check-box:selected .box {
    -fx-background-color: #1db954;
}
