<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.image.*?>

<BorderPane fx:id="mainPane" prefHeight="600.0" prefWidth="900.0" xmlns="http://javafx.com/javafx/13" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.dev.fplayer.controllers.MainPlayerController">
    <top>
        <VBox>
            <MenuBar fx:id="menuBar">
                <Menu text="File">
                    <MenuItem text="Open File..." onAction="#handleOpenFile">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/musical-note.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Open Directory..." onAction="#handleOpenDirectory">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/folder.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Open URL..." onAction="#handleOpenURL">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/globe.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <SeparatorMenuItem />
                    <MenuItem text="Exit" onAction="#handleExit">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/power-button.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="Playlist">
                    <MenuItem text="New Playlist" onAction="#handleNewPlaylist">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/list.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Save Playlist">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/save.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Load Playlist">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/folder-open.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <SeparatorMenuItem />
                    <MenuItem text="Edit Metadata" onAction="#handleEditMetadata">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/edit.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="View">
                    <CheckMenuItem text="Show Equalizer" selected="true">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/equalizer.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </CheckMenuItem>
                    <CheckMenuItem text="Show Library" selected="true">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/library.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </CheckMenuItem>
                    <CheckMenuItem text="Fullscreen">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/fullscreen.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </CheckMenuItem>
                    <SeparatorMenuItem />
                    <MenuItem text="Change Theme..." onAction="#handleChangeTheme">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/theme.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="Subtitles">
                    <MenuItem text="Load Subtitle File..." onAction="#handleLoadSubtitles">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/subtitle.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="Tools">
                    <MenuItem text="Video Downloader" onAction="#handleShowVideoDownloader">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/download.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Streaming Quality" onAction="#handleShowStreamingQuality">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/quality.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <SeparatorMenuItem />
                    <MenuItem text="Player Settings..." onAction="#handleSettings">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/settings.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="Help">
                    <MenuItem text="Keyboard Shortcuts" onAction="#handleShowKeyboardShortcuts">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/keyboard.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="About">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/info.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
            </MenuBar>
        </VBox>
    </top>

    <center>
        <SplitPane dividerPositions="0.7">
            <StackPane fx:id="mediaViewContainer" style="-fx-background-color: black;">
                <!-- MediaPlayerComponent will be added here programmatically -->
            </StackPane>

            <TabPane fx:id="sidebarTabPane" tabClosingPolicy="UNAVAILABLE">
                <Tab text="Playlist">
                    <VBox spacing="5">
                        <padding>
                            <Insets top="5" right="5" bottom="5" left="5" />
                        </padding>
                        <ListView fx:id="playlistView" VBox.vgrow="ALWAYS" />
                        <HBox spacing="5">
                            <Button>
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/plus.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Add" />
                                </tooltip>
                            </Button>
                            <Button>
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/minus.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Remove" />
                                </tooltip>
                            </Button>
                            <Button>
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/up-arrow.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Move Up" />
                                </tooltip>
                            </Button>
                            <Button>
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/down-arrow.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Move Down" />
                                </tooltip>
                            </Button>
                        </HBox>
                    </VBox>
                </Tab>
                <Tab text="Library">
                    <VBox spacing="5">
                        <padding>
                            <Insets top="5" right="5" bottom="5" left="5" />
                        </padding>
                        <ListView fx:id="libraryView" VBox.vgrow="ALWAYS" />
                    </VBox>
                </Tab>
                <Tab text="Playlists">
                    <VBox spacing="5">
                        <padding>
                            <Insets top="5" right="5" bottom="5" left="5" />
                        </padding>
                        <ListView fx:id="playlistsListView" VBox.vgrow="ALWAYS" />
                        <HBox spacing="5">
                            <Button onAction="#handleNewPlaylist">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/list.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="New Playlist" />
                                </tooltip>
                            </Button>
                            <Button>
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/cancel.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Delete Playlist" />
                                </tooltip>
                            </Button>
                            <Button>
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/edit.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Rename Playlist" />
                                </tooltip>
                            </Button>
                        </HBox>
                    </VBox>
                </Tab>
            </TabPane>
        </SplitPane>
    </center>

    <bottom>
        <VBox fx:id="controlsContainer" spacing="5">
            <padding>
                <Insets top="5" right="10" bottom="10" left="10" />
            </padding>

            <HBox alignment="CENTER">
                <Label fx:id="currentTimeLabel" text="00:00" />
                <Slider fx:id="timeSlider" HBox.hgrow="ALWAYS">
                    <HBox.margin>
                        <Insets left="5" right="5" />
                    </HBox.margin>
                </Slider>
                <Label fx:id="totalTimeLabel" text="00:00" />
            </HBox>

            <HBox fx:id="transportControls" alignment="CENTER" spacing="15">
                <HBox alignment="CENTER" spacing="10">
                    <Button fx:id="prevButton" onAction="#handlePrevious">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/previous.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Previous" />
                        </tooltip>
                    </Button>
                    <Button fx:id="playButton" onAction="#handlePlay">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/play-button.png" requestedWidth="32" requestedHeight="32" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Play" />
                        </tooltip>
                    </Button>
                    <Button fx:id="pauseButton" onAction="#handlePause">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/pause.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Pause" />
                        </tooltip>
                    </Button>
                    <Button fx:id="stopButton" onAction="#handleStop">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/stop.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Stop" />
                        </tooltip>
                    </Button>
                    <Button fx:id="nextButton" onAction="#handleNext">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/next.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Next" />
                        </tooltip>
                    </Button>
                </HBox>

                <Separator orientation="VERTICAL" />

                <HBox alignment="CENTER" spacing="5">
                    <Button fx:id="muteButton" onAction="#handleMute">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/volume.png" requestedWidth="20" requestedHeight="20" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Mute" />
                        </tooltip>
                    </Button>
                    <Slider fx:id="volumeSlider" min="0" max="1" value="1" prefWidth="100" onValueChange="#handleVolumeChange" />
                </HBox>

                <Separator orientation="VERTICAL" />

                <HBox alignment="CENTER" spacing="5">
                    <Button fx:id="fullscreenButton" onAction="#handleFullscreen">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/fullscreen.png" requestedWidth="20" requestedHeight="20" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Fullscreen" />
                        </tooltip>
                    </Button>
                    <Button fx:id="settingsButton" onAction="#handleSettings">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/settings.png" requestedWidth="20" requestedHeight="20" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Settings" />
                        </tooltip>
                    </Button>
                </HBox>
            </HBox>

            <TabPane fx:id="effectsTabPane" tabClosingPolicy="UNAVAILABLE">
                <!-- EqualizerComponent and SoundEffectsComponent will be added here programmatically -->
            </TabPane>
        </VBox>
    </bottom>
</BorderPane>
