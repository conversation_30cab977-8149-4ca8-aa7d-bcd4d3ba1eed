<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.image.*?>

<BorderPane fx:id="mainPane" minHeight="480.0" minWidth="640.0" prefHeight="768.0" prefWidth="1280.0" maxHeight="Infinity" maxWidth="Infinity" xmlns="http://javafx.com/javafx/13" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.dev.fplayer.controllers.MainPlayerController" styleClass="main-pane">
    <top>
        <VBox>
            <!-- Header component will be added programmatically -->
            <HBox fx:id="headerContainer" styleClass="header-placeholder" />
            <MenuBar fx:id="menuBar">
                <Menu text="File">
                    <MenuItem text="Open File..." onAction="#handleOpenFile">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/musical-note.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Open Directory..." onAction="#handleOpenDirectory">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/folder.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Open URL..." onAction="#handleOpenURL">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/globe.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <SeparatorMenuItem />
                    <MenuItem text="Exit" onAction="#handleExit">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/power-button.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="Playlist">
                    <MenuItem text="New Playlist" onAction="#handleNewPlaylist">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/list.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Save Playlist">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/save.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Load Playlist">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/folder-open.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <SeparatorMenuItem />
                    <MenuItem text="Edit Metadata" onAction="#handleEditMetadata">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/edit.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="View">
                    <CheckMenuItem text="Show Equalizer" selected="true">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/equalizer.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </CheckMenuItem>
                    <CheckMenuItem text="Show Library" selected="true">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/library.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </CheckMenuItem>
                    <CheckMenuItem text="Fullscreen">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/fullscreen.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </CheckMenuItem>
                    <SeparatorMenuItem />
                    <MenuItem text="Change Theme..." onAction="#handleChangeTheme">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/theme.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="Subtitles">
                    <MenuItem text="Load Subtitle File..." onAction="#handleLoadSubtitles">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/subtitle.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="Tools">
                    <MenuItem text="Video Downloader" onAction="#handleShowVideoDownloader">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/download.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="Streaming Quality" onAction="#handleShowStreamingQuality">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/quality.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <SeparatorMenuItem />
                    <MenuItem text="Player Settings..." onAction="#handleSettings">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/settings.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
                <Menu text="Help">
                    <MenuItem text="Keyboard Shortcuts" onAction="#handleShowKeyboardShortcuts">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/keyboard.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                    <MenuItem text="About">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/info.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                    </MenuItem>
                </Menu>
            </MenuBar>
        </VBox>
    </top>

    <center>
        <SplitPane dividerPositions="0.75" styleClass="main-split-pane" orientation="HORIZONTAL">
            <StackPane fx:id="mediaViewContainer" styleClass="media-container" SplitPane.resizableWithParent="true" minWidth="200" minHeight="112" prefWidth="640" prefHeight="360">
                <!-- MediaPlayerComponent will be added here programmatically -->
                <padding>
                    <Insets top="2" right="2" bottom="2" left="2" />
                </padding>
            </StackPane>

            <TabPane fx:id="sidebarTabPane" tabClosingPolicy="UNAVAILABLE" SplitPane.resizableWithParent="true" minWidth="150" prefWidth="250" maxWidth="400">
                <Tab text="Playlist">
                    <VBox spacing="10" styleClass="sidebar-content">
                        <padding>
                            <Insets top="5" right="5" bottom="5" left="5" />
                        </padding>
                        <Label text="Current Playlist" styleClass="section-header" />
                        <ListView fx:id="playlistView" VBox.vgrow="ALWAYS" styleClass="playlist-list-view" />
                        <HBox spacing="8" alignment="CENTER">
                            <Button styleClass="playlist-button">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/plus.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Add" />
                                </tooltip>
                            </Button>
                            <Button styleClass="playlist-button">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/minus.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Remove" />
                                </tooltip>
                            </Button>
                            <Button styleClass="playlist-button">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/up-arrow.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Move Up" />
                                </tooltip>
                            </Button>
                            <Button styleClass="playlist-button">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/down-arrow.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Move Down" />
                                </tooltip>
                            </Button>
                        </HBox>
                    </VBox>
                </Tab>
                <Tab text="Library">
                    <VBox spacing="10" styleClass="sidebar-content">
                        <padding>
                            <Insets top="5" right="5" bottom="5" left="5" />
                        </padding>
                        <Label text="Media Library" styleClass="section-header" />
                        <HBox spacing="8" alignment="CENTER_LEFT">
                            <TextField promptText="Search library..." HBox.hgrow="ALWAYS" style="-fx-background-color: #333333; -fx-text-fill: white; -fx-prompt-text-fill: #888888;" />
                            <Button styleClass="search-button" style="-fx-background-color: #1DB954;">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/search.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                            </Button>
                        </HBox>
                        <ListView fx:id="libraryView" VBox.vgrow="ALWAYS" styleClass="library-list-view" />
                    </VBox>
                </Tab>
                <Tab text="Playlists">
                    <VBox spacing="10" styleClass="sidebar-content">
                        <padding>
                            <Insets top="5" right="5" bottom="5" left="5" />
                        </padding>
                        <Label text="My Playlists" styleClass="section-header" />
                        <ListView fx:id="playlistsListView" VBox.vgrow="ALWAYS" styleClass="playlists-list-view" />
                        <HBox spacing="8" alignment="CENTER">
                            <Button onAction="#handleNewPlaylist" styleClass="playlist-button" style="-fx-background-color: #1DB954;">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/list.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="New Playlist" />
                                </tooltip>
                            </Button>
                            <Button styleClass="playlist-button">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/cancel.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Delete Playlist" />
                                </tooltip>
                            </Button>
                            <Button styleClass="playlist-button">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/edit.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Rename Playlist" />
                                </tooltip>
                            </Button>
                            <Button styleClass="playlist-button">
                                <graphic>
                                    <ImageView>
                                        <Image url="@../assets/save.png" requestedWidth="16" requestedHeight="16" preserveRatio="true" />
                                    </ImageView>
                                </graphic>
                                <tooltip>
                                    <Tooltip text="Save Playlist" />
                                </tooltip>
                            </Button>
                        </HBox>
                    </VBox>
                </Tab>
            </TabPane>
        </SplitPane>
    </center>

    <bottom>
        <VBox fx:id="controlsContainer" spacing="10" styleClass="controls-container" style="-fx-background-color: #1e1e1e; -fx-border-color: #333333; -fx-border-width: 1px 0 0 0;">
            <padding>
                <Insets top="10" right="15" bottom="15" left="15" />
            </padding>

            <HBox alignment="CENTER" styleClass="time-slider-container">
                <Label fx:id="currentTimeLabel" text="00:00" style="-fx-text-fill: white; -fx-font-weight: bold;" />
                <Slider fx:id="timeSlider" HBox.hgrow="ALWAYS" styleClass="time-slider" style="-fx-control-inner-background: #333333; -fx-accent: #1DB954;">
                    <HBox.margin>
                        <Insets left="10" right="10" />
                    </HBox.margin>
                </Slider>
                <Label fx:id="totalTimeLabel" text="00:00" style="-fx-text-fill: white; -fx-font-weight: bold;" />
            </HBox>

            <HBox fx:id="transportControls" alignment="CENTER" spacing="20" styleClass="transport-controls" style="-fx-background-color: #252525; -fx-padding: 10; -fx-background-radius: 5;">
                <HBox alignment="CENTER" spacing="10">
                    <Button fx:id="prevButton" onAction="#handlePrevious">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/previous.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Previous" />
                        </tooltip>
                    </Button>
                    <Button fx:id="playButton" onAction="#handlePlay">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/play-button.png" requestedWidth="32" requestedHeight="32" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Play" />
                        </tooltip>
                    </Button>
                    <Button fx:id="pauseButton" onAction="#handlePause">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/pause.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Pause" />
                        </tooltip>
                    </Button>
                    <Button fx:id="stopButton" onAction="#handleStop">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/stop.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Stop" />
                        </tooltip>
                    </Button>
                    <Button fx:id="nextButton" onAction="#handleNext">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/next.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Next" />
                        </tooltip>
                    </Button>
                </HBox>

                <Separator orientation="VERTICAL" />

                <HBox alignment="CENTER" spacing="8" styleClass="volume-control">
                    <Button fx:id="muteButton" onAction="#handleMute" style="-fx-background-color: transparent;">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/volume.png" requestedWidth="20" requestedHeight="20" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Mute" />
                        </tooltip>
                    </Button>
                    <Slider fx:id="volumeSlider" min="0" max="1" value="1" prefWidth="120" onValueChange="#handleVolumeChange"
                            style="-fx-control-inner-background: #333333; -fx-accent: #1DB954;" styleClass="volume-slider" />
                </HBox>

                <Separator orientation="VERTICAL" />

                <HBox alignment="CENTER" spacing="8" styleClass="control-buttons">
                    <Button fx:id="fullscreenButton" onAction="#handleFullscreen" style="-fx-background-color: transparent;">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/fullscreen.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Fullscreen" />
                        </tooltip>
                    </Button>
                    <Button fx:id="settingsButton" onAction="#handleSettings" style="-fx-background-color: transparent;">
                        <graphic>
                            <ImageView>
                                <Image url="@../assets/settings.png" requestedWidth="24" requestedHeight="24" preserveRatio="true" />
                            </ImageView>
                        </graphic>
                        <tooltip>
                            <Tooltip text="Settings" />
                        </tooltip>
                    </Button>
                </HBox>
            </HBox>

            <TabPane fx:id="effectsTabPane" tabClosingPolicy="UNAVAILABLE" styleClass="effects-tab-pane">
                <!-- Components will be added here programmatically -->
                <padding>
                    <Insets top="3" right="3" bottom="3" left="3" />
                </padding>
                <VBox.margin>
                    <Insets top="3" right="0" bottom="0" left="0" />
                </VBox.margin>
                <!-- No placeholder tabs - they will be added programmatically -->
            </TabPane>
        </VBox>
    </bottom>
</BorderPane>
