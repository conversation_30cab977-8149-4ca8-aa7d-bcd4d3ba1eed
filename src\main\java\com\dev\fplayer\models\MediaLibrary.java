package com.dev.fplayer.models;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Manages the media library, including all media items and playlists
 */
public class MediaLibrary implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private transient ObservableList<MediaItem> allMedia;
    private transient ObservableList<MediaItem> audioMedia;
    private transient ObservableList<MediaItem> videoMedia;
    private transient ObservableList<Playlist> playlists;
    
    // For serialization
    private List<String> allMediaPaths;
    private List<Playlist> playlistData;
    
    /**
     * Create a new empty media library
     */
    public MediaLibrary() {
        allMedia = FXCollections.observableArrayList();
        audioMedia = FXCollections.observableArrayList();
        videoMedia = FXCollections.observableArrayList();
        playlists = FXCollections.observableArrayList();
        
        allMediaPaths = new ArrayList<>();
        playlistData = new ArrayList<>();
    }
    
    /**
     * Add a media item to the library
     * @param item The media item to add
     */
    public void addMediaItem(MediaItem item) {
        if (!containsMediaItem(item)) {
            allMedia.add(item);
            
            if (item.getType() == MediaItem.MediaType.AUDIO) {
                audioMedia.add(item);
            } else if (item.getType() == MediaItem.MediaType.VIDEO) {
                videoMedia.add(item);
            }
            
            // Add to paths for serialization
            if (item.getFile() != null) {
                allMediaPaths.add(item.getFile().getAbsolutePath());
            } else if (item.getUrl() != null) {
                allMediaPaths.add(item.getUrl());
            }
        }
    }
    
    /**
     * Check if the library contains a media item
     * @param item The media item to check
     * @return True if the library contains the item, false otherwise
     */
    public boolean containsMediaItem(MediaItem item) {
        if (item.getFile() != null) {
            for (MediaItem existingItem : allMedia) {
                if (existingItem.getFile() != null && 
                    existingItem.getFile().getAbsolutePath().equals(item.getFile().getAbsolutePath())) {
                    return true;
                }
            }
        } else if (item.getUrl() != null) {
            for (MediaItem existingItem : allMedia) {
                if (existingItem.getUrl() != null && existingItem.getUrl().equals(item.getUrl())) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * Remove a media item from the library
     * @param item The media item to remove
     */
    public void removeMediaItem(MediaItem item) {
        allMedia.remove(item);
        audioMedia.remove(item);
        videoMedia.remove(item);
        
        // Remove from paths for serialization
        if (item.getFile() != null) {
            allMediaPaths.remove(item.getFile().getAbsolutePath());
        } else if (item.getUrl() != null) {
            allMediaPaths.remove(item.getUrl());
        }
        
        // Remove from all playlists
        for (Playlist playlist : playlists) {
            playlist.removeItem(item);
        }
    }
    
    /**
     * Add a playlist to the library
     * @param playlist The playlist to add
     */
    public void addPlaylist(Playlist playlist) {
        playlists.add(playlist);
        playlistData.add(playlist);
    }
    
    /**
     * Remove a playlist from the library
     * @param playlist The playlist to remove
     */
    public void removePlaylist(Playlist playlist) {
        playlists.remove(playlist);
        playlistData.remove(playlist);
    }
    
    /**
     * Scan a directory for media files and add them to the library
     * @param directory The directory to scan
     */
    public void scanDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        scanDirectory(file);
                    } else {
                        String name = file.getName().toLowerCase();
                        if (name.endsWith(".mp3") || name.endsWith(".wav") || name.endsWith(".aac") || 
                            name.endsWith(".flac") || name.endsWith(".ogg") || name.endsWith(".m4a") ||
                            name.endsWith(".mp4") || name.endsWith(".avi") || name.endsWith(".mkv") || 
                            name.endsWith(".mov") || name.endsWith(".wmv") || name.endsWith(".flv")) {
                            
                            MediaItem item = new MediaItem(file);
                            addMediaItem(item);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Get all media items in the library
     * @return The observable list of all media items
     */
    public ObservableList<MediaItem> getAllMedia() {
        return allMedia;
    }
    
    /**
     * Get all audio media items in the library
     * @return The observable list of audio media items
     */
    public ObservableList<MediaItem> getAudioMedia() {
        return audioMedia;
    }
    
    /**
     * Get all video media items in the library
     * @return The observable list of video media items
     */
    public ObservableList<MediaItem> getVideoMedia() {
        return videoMedia;
    }
    
    /**
     * Get all playlists in the library
     * @return The observable list of playlists
     */
    public ObservableList<Playlist> getPlaylists() {
        return playlists;
    }
    
    /**
     * Get the list of all media paths (for serialization)
     * @return The list of all media paths
     */
    public List<String> getAllMediaPaths() {
        return allMediaPaths;
    }
    
    /**
     * Set the list of all media paths and rebuild the media lists
     * @param allMediaPaths The list of all media paths
     */
    public void setAllMediaPaths(List<String> allMediaPaths) {
        this.allMediaPaths = allMediaPaths;
        rebuildMediaFromPaths();
    }
    
    /**
     * Get the list of playlist data (for serialization)
     * @return The list of playlist data
     */
    public List<Playlist> getPlaylistData() {
        return playlistData;
    }
    
    /**
     * Set the list of playlist data and rebuild the playlists list
     * @param playlistData The list of playlist data
     */
    public void setPlaylistData(List<Playlist> playlistData) {
        this.playlistData = playlistData;
        rebuildPlaylistsFromData();
    }
    
    /**
     * Rebuild the media lists from the paths
     */
    private void rebuildMediaFromPaths() {
        allMedia.clear();
        audioMedia.clear();
        videoMedia.clear();
        
        for (String path : allMediaPaths) {
            if (path.startsWith("http://") || path.startsWith("https://")) {
                // URL
                MediaItem item = new MediaItem(path, new File(path).getName(), MediaItem.MediaType.UNKNOWN);
                allMedia.add(item);
            } else {
                // File
                File file = new File(path);
                if (file.exists()) {
                    MediaItem item = new MediaItem(file);
                    allMedia.add(item);
                    
                    if (item.getType() == MediaItem.MediaType.AUDIO) {
                        audioMedia.add(item);
                    } else if (item.getType() == MediaItem.MediaType.VIDEO) {
                        videoMedia.add(item);
                    }
                }
            }
        }
    }
    
    /**
     * Rebuild the playlists list from the data
     */
    private void rebuildPlaylistsFromData() {
        playlists.clear();
        playlists.addAll(playlistData);
    }
}
