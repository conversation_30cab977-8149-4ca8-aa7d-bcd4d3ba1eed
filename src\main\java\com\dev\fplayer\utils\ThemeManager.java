package com.dev.fplayer.utils;

import javafx.scene.Scene;

import java.util.HashMap;
import java.util.Map;

/**
 * Manages application themes/skins
 */
public class ThemeManager {

    private static ThemeManager instance;

    private Scene scene;
    private String currentTheme;
    private Map<String, String> themes;

    /**
     * Get the singleton instance
     * 
     * @return The ThemeManager instance
     */
    public static ThemeManager getInstance() {
        if (instance == null) {
            instance = new ThemeManager();
        }
        return instance;
    }

    /**
     * Private constructor for singleton
     */
    private ThemeManager() {
        themes = new HashMap<>();

        // Register built-in themes
        themes.put("Dark", "css/dark_theme.css");
        themes.put("Light", "css/light_theme.css");
        themes.put("Blue", "css/blue_theme.css");
        themes.put("Purple", "css/purple_theme.css");
        themes.put("Green", "css/green_theme.css");
        themes.put("High Contrast", "css/high_contrast_theme.css");
    }

    /**
     * Initialize the theme manager with a scene
     * 
     * @param scene        The scene to manage themes for
     * @param defaultTheme The default theme to use
     */
    public void initialize(Scene scene, String defaultTheme) {
        this.scene = scene;
        setTheme(defaultTheme);
    }

    /**
     * Set the current theme
     * 
     * @param themeName The name of the theme to set
     */
    public void setTheme(String themeName) {
        if (scene == null) {
            throw new IllegalStateException("Theme manager not initialized with a scene");
        }

        if (!themes.containsKey(themeName)) {
            throw new IllegalArgumentException("Unknown theme: " + themeName);
        }

        // Remove current theme if any
        if (currentTheme != null && themes.containsKey(currentTheme)) {
            scene.getStylesheets()
                    .remove(getClass().getResource("/com/dev/fplayer/" + themes.get(currentTheme)).toExternalForm());
        }

        // Add new theme
        scene.getStylesheets()
                .add(getClass().getResource("/com/dev/fplayer/" + themes.get(themeName)).toExternalForm());
        currentTheme = themeName;
    }

    /**
     * Get the current theme name
     * 
     * @return The current theme name
     */
    public String getCurrentTheme() {
        return currentTheme;
    }

    /**
     * Get all available themes
     * 
     * @return Map of theme names to CSS file paths
     */
    public Map<String, String> getThemes() {
        return themes;
    }

    /**
     * Register a custom theme
     * 
     * @param name    The name of the theme
     * @param cssPath The path to the CSS file
     */
    public void registerTheme(String name, String cssPath) {
        themes.put(name, cssPath);
    }
}
