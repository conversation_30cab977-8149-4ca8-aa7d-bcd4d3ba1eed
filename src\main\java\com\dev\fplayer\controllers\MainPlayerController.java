package com.dev.fplayer.controllers;

import com.dev.fplayer.components.AudioVisualizationComponent;
import com.dev.fplayer.components.EqualizerComponent;
import com.dev.fplayer.components.KeyboardShortcutsDialog;
import com.dev.fplayer.components.MediaPlayerComponent;
import com.dev.fplayer.components.MetadataEditorDialog;
import com.dev.fplayer.components.PlayerSettingsDialog;
import com.dev.fplayer.components.SoundEffectsComponent;
import com.dev.fplayer.components.StreamingDialog;
import com.dev.fplayer.components.StreamingQualitySelector;
import com.dev.fplayer.components.SubtitleComponent;
import com.dev.fplayer.components.ThemeSelectorDialog;
import com.dev.fplayer.components.VideoDownloaderComponent;
import com.dev.fplayer.utils.KeyboardShortcutManager;
import com.dev.fplayer.utils.ThemeManager;
import com.dev.fplayer.models.MediaItem;
import com.dev.fplayer.models.MediaLibrary;
import com.dev.fplayer.models.Playlist;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.scene.media.MediaPlayer;
import javafx.stage.DirectoryChooser;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import javafx.util.Duration;

import java.io.File;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Main controller for the media player application
 */
public class MainPlayerController implements Initializable {

    @FXML
    private BorderPane mainPane;

    @FXML
    private MenuBar menuBar;

    @FXML
    private StackPane mediaViewContainer;

    @FXML
    private VBox controlsContainer;

    @FXML
    private HBox transportControls;

    @FXML
    private Button playButton;

    @FXML
    private Button pauseButton;

    @FXML
    private Button stopButton;

    @FXML
    private Button prevButton;

    @FXML
    private Button nextButton;

    @FXML
    private Button muteButton;

    @FXML
    private Button fullscreenButton;

    @FXML
    private Button settingsButton;

    @FXML
    private Slider volumeSlider;

    @FXML
    private Slider timeSlider;

    @FXML
    private Label currentTimeLabel;

    @FXML
    private Label totalTimeLabel;

    @FXML
    private TabPane sidebarTabPane;

    @FXML
    private ListView<MediaItem> playlistView;

    @FXML
    private ListView<MediaItem> libraryView;

    @FXML
    private ListView<Playlist> playlistsListView;

    @FXML
    private TabPane effectsTabPane;

    // Components
    private MediaPlayerComponent mediaPlayerComponent;
    private EqualizerComponent equalizerComponent;
    private SoundEffectsComponent soundEffectsComponent;
    private AudioVisualizationComponent visualizationComponent;
    private SubtitleComponent subtitleComponent;
    private StreamingQualitySelector streamingQualitySelector;
    private VideoDownloaderComponent videoDownloaderComponent;

    // Models
    private MediaLibrary mediaLibrary;
    private ObjectProperty<Playlist> currentPlaylist;
    private int currentItemIndex = -1;

    // State variables
    private double lastVolume = 0.5;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // Initialize models
        mediaLibrary = new MediaLibrary();
        currentPlaylist = new SimpleObjectProperty<>(new Playlist("Now Playing"));

        // Initialize components
        initializeMediaPlayer();
        initializeEqualizer();
        initializeSoundEffects();
        initializeVisualization();
        initializeSubtitles();
        initializeStreamingQuality();
        initializeVideoDownloader();
        initializeBindings();
        initializeListeners();
        initializeKeyboardShortcuts();

        // Add the current playlist to the library
        mediaLibrary.addPlaylist(currentPlaylist.get());

        // Bind playlist view to current playlist
        playlistView.setItems(currentPlaylist.get().getItems());

        // Bind library view to all media
        libraryView.setItems(mediaLibrary.getAllMedia());

        // Bind playlists list view to all playlists
        playlistsListView.setItems(mediaLibrary.getPlaylists());

        // Set up a listener to initialize keyboard shortcuts when the scene is
        // available
        mainPane.sceneProperty().addListener((obs, oldScene, newScene) -> {
            if (newScene != null) {
                KeyboardShortcutManager.getInstance().setScene(newScene);
            }
        });
    }

    /**
     * Initialize the media player component
     */
    private void initializeMediaPlayer() {
        mediaPlayerComponent = new MediaPlayerComponent();
        mediaViewContainer.getChildren().add(mediaPlayerComponent);

        // Make the media player component fill the container
        mediaPlayerComponent.prefWidthProperty().bind(mediaViewContainer.widthProperty());
        mediaPlayerComponent.prefHeightProperty().bind(mediaViewContainer.heightProperty());
    }

    /**
     * Initialize the equalizer component
     */
    private void initializeEqualizer() {
        equalizerComponent = new EqualizerComponent();
        Tab equalizerTab = new Tab("Equalizer", equalizerComponent);
        equalizerTab.setClosable(false);
        effectsTabPane.getTabs().add(equalizerTab);
    }

    /**
     * Initialize the sound effects component
     */
    private void initializeSoundEffects() {
        soundEffectsComponent = new SoundEffectsComponent();
        Tab effectsTab = new Tab("Sound Effects", soundEffectsComponent);
        effectsTab.setClosable(false);
        effectsTabPane.getTabs().add(effectsTab);
    }

    /**
     * Initialize the visualization component
     */
    private void initializeVisualization() {
        visualizationComponent = new AudioVisualizationComponent();
        Tab visualizationTab = new Tab("Visualization", visualizationComponent);
        visualizationTab.setClosable(false);
        effectsTabPane.getTabs().add(visualizationTab);
    }

    /**
     * Initialize the subtitles component
     */
    private void initializeSubtitles() {
        subtitleComponent = new SubtitleComponent();
        Tab subtitlesTab = new Tab("Subtitles", subtitleComponent);
        subtitlesTab.setClosable(false);
        effectsTabPane.getTabs().add(subtitlesTab);
    }

    /**
     * Initialize the streaming quality selector component
     */
    private void initializeStreamingQuality() {
        streamingQualitySelector = new StreamingQualitySelector(mediaPlayerComponent);
        Tab streamingQualityTab = new Tab("Streaming Quality", streamingQualitySelector);
        streamingQualityTab.setClosable(false);
        effectsTabPane.getTabs().add(streamingQualityTab);
    }

    /**
     * Initialize the video downloader component
     */
    private void initializeVideoDownloader() {
        videoDownloaderComponent = new VideoDownloaderComponent();
        Tab videoDownloaderTab = new Tab("Video Downloader", videoDownloaderComponent);
        videoDownloaderTab.setClosable(false);
        effectsTabPane.getTabs().add(videoDownloaderTab);
    }

    /**
     * Initialize keyboard shortcuts
     */
    private void initializeKeyboardShortcuts() {
        KeyboardShortcutManager shortcutManager = KeyboardShortcutManager.getInstance();
        // Initialize with null scene for now, we'll set it later when it's available
        shortcutManager.initialize(null);

        // Register actions for playback controls
        shortcutManager.registerAction("play", event -> handlePlay());
        shortcutManager.registerAction("stop", event -> handleStop());
        shortcutManager.registerAction("next", event -> handleNext());
        shortcutManager.registerAction("previous", event -> handlePrevious());

        // Register actions for volume controls
        shortcutManager.registerAction("volumeUp", event -> {
            if (mediaPlayerComponent.getMediaPlayer() != null) {
                double volume = mediaPlayerComponent.getVolume();
                mediaPlayerComponent.setVolume(Math.min(1.0, volume + 0.1));
            }
        });

        shortcutManager.registerAction("volumeDown", event -> {
            if (mediaPlayerComponent.getMediaPlayer() != null) {
                double volume = mediaPlayerComponent.getVolume();
                mediaPlayerComponent.setVolume(Math.max(0.0, volume - 0.1));
            }
        });

        shortcutManager.registerAction("mute", event -> {
            if (mediaPlayerComponent.getMediaPlayer() != null) {
                // Toggle mute
                if (mediaPlayerComponent.getVolume() > 0) {
                    // Store current volume and set to 0
                    mediaPlayerComponent.setVolume(0);
                } else {
                    // Restore volume to 50% if it was 0
                    mediaPlayerComponent.setVolume(0.5);
                }
            }
        });

        // Register actions for seeking
        shortcutManager.registerAction("seekForward", event -> {
            if (mediaPlayerComponent.getMediaPlayer() != null) {
                double currentTime = mediaPlayerComponent.getMediaPlayer().getCurrentTime().toSeconds();
                mediaPlayerComponent.seek(javafx.util.Duration.seconds(currentTime + 10));
            }
        });

        shortcutManager.registerAction("seekBackward", event -> {
            if (mediaPlayerComponent.getMediaPlayer() != null) {
                double currentTime = mediaPlayerComponent.getMediaPlayer().getCurrentTime().toSeconds();
                mediaPlayerComponent.seek(javafx.util.Duration.seconds(Math.max(0, currentTime - 10)));
            }
        });

        shortcutManager.registerAction("seekForwardLarge", event -> {
            if (mediaPlayerComponent.getMediaPlayer() != null) {
                double currentTime = mediaPlayerComponent.getMediaPlayer().getCurrentTime().toSeconds();
                mediaPlayerComponent.seek(javafx.util.Duration.seconds(currentTime + 60));
            }
        });

        shortcutManager.registerAction("seekBackwardLarge", event -> {
            if (mediaPlayerComponent.getMediaPlayer() != null) {
                double currentTime = mediaPlayerComponent.getMediaPlayer().getCurrentTime().toSeconds();
                mediaPlayerComponent.seek(javafx.util.Duration.seconds(Math.max(0, currentTime - 60)));
            }
        });

        // Register actions for file operations
        shortcutManager.registerAction("openFile", event -> handleOpenFile());
        shortcutManager.registerAction("openURL", event -> handleOpenURL());

        // Register actions for playlist operations
        shortcutManager.registerAction("newPlaylist", event -> handleNewPlaylist());

        // Register actions for view controls
        shortcutManager.registerAction("fullscreen", event -> {
            if (mainPane.getScene() != null && mainPane.getScene().getWindow() != null) {
                Stage stage = (Stage) mainPane.getScene().getWindow();
                stage.setFullScreen(!stage.isFullScreen());
            }
        });

        // Register actions for application controls
        shortcutManager.registerAction("exit", event -> handleExit());
        shortcutManager.registerAction("help", event -> handleShowKeyboardShortcuts());

        // Register actions for subtitle controls
        shortcutManager.registerAction("loadSubtitles", event -> handleLoadSubtitles());
    }

    /**
     * Initialize bindings between components
     */
    private void initializeBindings() {
        // Disable transport controls when no media is loaded
        transportControls.disableProperty().bind(
                Bindings.createBooleanBinding(() -> mediaPlayerComponent.getMediaPlayer() == null,
                        mediaPlayerComponent.mediaPlayerProperty()));

        // We'll handle time slider binding in the playMedia method
    }

    /**
     * Initialize listeners for UI components
     */
    private void initializeListeners() {
        // Playlist view selection listener
        playlistView.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                playMedia(newVal);
                currentItemIndex = playlistView.getSelectionModel().getSelectedIndex();
            }
        });

        // Library view selection listener
        libraryView.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                // Add to current playlist if not already there
                if (!currentPlaylist.get().getItems().contains(newVal)) {
                    currentPlaylist.get().addItem(newVal);
                }

                // Select in playlist view
                playlistView.getSelectionModel().select(newVal);
            }
        });

        // Playlists list view selection listener
        playlistsListView.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                currentPlaylist.set(newVal);
                playlistView.setItems(currentPlaylist.get().getItems());
            }
        });

        // Time slider listener
        timeSlider.valueChangingProperty().addListener((obs, wasChanging, isChanging) -> {
            if (!isChanging && mediaPlayerComponent.getMediaPlayer() != null) {
                mediaPlayerComponent.getMediaPlayer().seek(Duration.seconds(timeSlider.getValue()));
            }
        });
    }

    /**
     * Play a media item
     *
     * @param item The media item to play
     */
    private void playMedia(MediaItem item) {
        if (item != null) {
            if (item.getFile() != null) {
                mediaPlayerComponent.loadMedia(item.getFile());
            } else if (item.getUrl() != null) {
                mediaPlayerComponent.loadMedia(item.getUrl());
            }

            // Connect effects to the new media player
            MediaPlayer player = mediaPlayerComponent.getMediaPlayer();
            if (player != null) {
                equalizerComponent.connectToMediaPlayer(player);
                soundEffectsComponent.connectToMediaPlayer(player);

                // Connect and start visualization
                visualizationComponent.connectToMediaPlayer(player);
                visualizationComponent.start();

                // Connect subtitles
                subtitleComponent.connectToMediaPlayer(player);

                // Update time display
                player.currentTimeProperty().addListener((obs, oldVal, newVal) -> {
                    updateTimeDisplay(newVal, player.getTotalDuration());
                });

                player.totalDurationProperty().addListener((obs, oldVal, newVal) -> {
                    updateTimeDisplay(player.getCurrentTime(), newVal);
                });

                // Set up end of media handler
                player.setOnEndOfMedia(() -> {
                    playNextItem();
                });

                // Initialize time slider
                timeSlider.setMin(0);
                timeSlider.setMax(player.getTotalDuration().toSeconds());
                timeSlider.setValue(0);

                // Bind time slider to current time
                player.currentTimeProperty().addListener((obs, oldVal, newVal) -> {
                    if (!timeSlider.isValueChanging()) {
                        timeSlider.setValue(newVal.toSeconds());
                    }
                });
            }
        }
    }

    /**
     * Update the time display labels
     *
     * @param currentTime The current playback time
     * @param totalTime   The total duration of the media
     */
    private void updateTimeDisplay(Duration currentTime, Duration totalTime) {
        if (currentTime != null) {
            currentTimeLabel.setText(formatTime(currentTime));
        }

        if (totalTime != null) {
            totalTimeLabel.setText(formatTime(totalTime));
        }
    }

    /**
     * Format a duration as a time string (HH:MM:SS)
     *
     * @param duration The duration to format
     * @return The formatted time string
     */
    private String formatTime(Duration duration) {
        int hours = (int) duration.toHours();
        int minutes = (int) duration.toMinutes() % 60;
        int seconds = (int) duration.toSeconds() % 60;

        if (hours > 0) {
            return String.format("%d:%02d:%02d", hours, minutes, seconds);
        } else {
            return String.format("%02d:%02d", minutes, seconds);
        }
    }

    /**
     * Play the next item in the playlist
     */
    private void playNextItem() {
        if (currentPlaylist.get() != null && !currentPlaylist.get().isEmpty()) {
            int nextIndex = currentItemIndex + 1;
            if (nextIndex >= currentPlaylist.get().getItems().size()) {
                nextIndex = 0; // Loop back to the beginning
            }

            currentItemIndex = nextIndex;
            MediaItem nextItem = currentPlaylist.get().getItems().get(nextIndex);
            playlistView.getSelectionModel().select(nextIndex);
            playMedia(nextItem);
        }
    }

    /**
     * Play the previous item in the playlist
     */
    private void playPreviousItem() {
        if (currentPlaylist.get() != null && !currentPlaylist.get().isEmpty()) {
            int prevIndex = currentItemIndex - 1;
            if (prevIndex < 0) {
                prevIndex = currentPlaylist.get().getItems().size() - 1; // Loop to the end
            }

            currentItemIndex = prevIndex;
            MediaItem prevItem = currentPlaylist.get().getItems().get(prevIndex);
            playlistView.getSelectionModel().select(prevIndex);
            playMedia(prevItem);
        }
    }

    // FXML event handlers

    @FXML
    private void handlePlay() {
        if (mediaPlayerComponent.getMediaPlayer() != null) {
            mediaPlayerComponent.play();
        } else if (!currentPlaylist.get().isEmpty()) {
            // Play the first item if nothing is playing
            currentItemIndex = 0;
            playlistView.getSelectionModel().select(0);
            playMedia(currentPlaylist.get().getItems().get(0));
        }
    }

    @FXML
    private void handlePause() {
        if (mediaPlayerComponent.getMediaPlayer() != null) {
            mediaPlayerComponent.pause();
        }
    }

    @FXML
    private void handleStop() {
        if (mediaPlayerComponent.getMediaPlayer() != null) {
            mediaPlayerComponent.stop();
            visualizationComponent.stop();
        }
    }

    @FXML
    private void handlePrevious() {
        playPreviousItem();
    }

    @FXML
    private void handleNext() {
        playNextItem();
    }

    @FXML
    private void handleOpenFile() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Open Media File");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Media Files", "*.mp3", "*.wav", "*.mp4", "*.avi", "*.mkv", "*.flv"),
                new FileChooser.ExtensionFilter("Audio Files", "*.mp3", "*.wav", "*.aac", "*.flac", "*.ogg", "*.m4a"),
                new FileChooser.ExtensionFilter("Video Files", "*.mp4", "*.avi", "*.mkv", "*.mov", "*.wmv", "*.flv"),
                new FileChooser.ExtensionFilter("All Files", "*.*"));

        List<File> files = fileChooser.showOpenMultipleDialog(mainPane.getScene().getWindow());
        if (files != null) {
            for (File file : files) {
                MediaItem item = new MediaItem(file);
                mediaLibrary.addMediaItem(item);
                currentPlaylist.get().addItem(item);
            }

            // Play the first selected file
            if (!files.isEmpty()) {
                playlistView.getSelectionModel().select(currentPlaylist.get().size() - files.size());
            }
        }
    }

    @FXML
    private void handleOpenDirectory() {
        DirectoryChooser directoryChooser = new DirectoryChooser();
        directoryChooser.setTitle("Open Media Directory");

        File directory = directoryChooser.showDialog(mainPane.getScene().getWindow());
        if (directory != null) {
            mediaLibrary.scanDirectory(directory);
        }
    }

    @FXML
    private void handleNewPlaylist() {
        TextInputDialog dialog = new TextInputDialog("New Playlist");
        dialog.setTitle("New Playlist");
        dialog.setHeaderText("Create a new playlist");
        dialog.setContentText("Enter playlist name:");

        dialog.showAndWait().ifPresent(name -> {
            Playlist playlist = new Playlist(name);
            mediaLibrary.addPlaylist(playlist);
            playlistsListView.getSelectionModel().select(playlist);
        });
    }

    @FXML
    private void handleExit() {
        // Clean up resources
        if (visualizationComponent != null) {
            visualizationComponent.stop();
        }
        mediaPlayerComponent.dispose();

        // Exit the application
        javafx.application.Platform.exit();
    }

    @FXML
    private void handleChangeTheme() {
        ThemeSelectorDialog dialog = new ThemeSelectorDialog();
        dialog.showAndWait().ifPresent(themeName -> {
            ThemeManager.getInstance().setTheme(themeName);
        });
    }

    @FXML
    private void handleEditMetadata() {
        // Get the selected media item
        MediaItem selectedItem = null;

        if (currentPlaylist.get() != null && !currentPlaylist.get().isEmpty()) {
            // Get the selected item in the playlist
            int selectedIndex = playlistView.getSelectionModel().getSelectedIndex();
            if (selectedIndex >= 0 && selectedIndex < currentPlaylist.get().getItems().size()) {
                selectedItem = currentPlaylist.get().getItems().get(selectedIndex);
            } else if (currentItemIndex >= 0 && currentItemIndex < currentPlaylist.get().getItems().size()) {
                // Use the currently playing item if nothing is selected
                selectedItem = currentPlaylist.get().getItems().get(currentItemIndex);
            }
        }

        if (selectedItem != null) {
            // Open the metadata editor dialog
            MetadataEditorDialog dialog = new MetadataEditorDialog(selectedItem);
            dialog.showAndWait().ifPresent(updatedItem -> {
                // Update the playlist view
                playlistView.refresh();

                // If this is the currently playing item, update the display
                if (currentItemIndex >= 0 &&
                        currentItemIndex < currentPlaylist.get().getItems().size() &&
                        currentPlaylist.get().getItems().get(currentItemIndex) == updatedItem) {
                    // Update time display to refresh the UI
                    if (mediaPlayerComponent.getMediaPlayer() != null) {
                        updateTimeDisplay(
                                mediaPlayerComponent.getMediaPlayer().getCurrentTime(),
                                mediaPlayerComponent.getMediaPlayer().getTotalDuration());
                    }
                }
            });
        } else {
            // Show an error if no item is selected
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("No Media Selected");
            alert.setHeaderText("No media item selected");
            alert.setContentText("Please select a media item from the playlist to edit its metadata.");
            alert.showAndWait();
        }
    }

    @FXML
    private void handleLoadSubtitles() {
        if (mediaPlayerComponent.getMediaPlayer() == null) {
            // Show an error if no media is playing
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("No Media Playing");
            alert.setHeaderText("No media is currently playing");
            alert.setContentText("Please start playing a media file before loading subtitles.");
            alert.showAndWait();
            return;
        }

        // Open file chooser for subtitle files
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Open Subtitle File");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Subtitle Files", "*.srt", "*.vtt", "*.sub"),
                new FileChooser.ExtensionFilter("SRT Files", "*.srt"),
                new FileChooser.ExtensionFilter("WebVTT Files", "*.vtt"),
                new FileChooser.ExtensionFilter("SUB Files", "*.sub"),
                new FileChooser.ExtensionFilter("All Files", "*.*"));

        File file = fileChooser.showOpenDialog(mainPane.getScene().getWindow());
        if (file != null) {
            // Load the subtitle file
            subtitleComponent.setSubtitleFile(file);

            // Show the subtitles tab
            for (Tab tab : effectsTabPane.getTabs()) {
                if (tab.getText().equals("Subtitles")) {
                    effectsTabPane.getSelectionModel().select(tab);
                    break;
                }
            }
        }
    }

    @FXML
    private void handleOpenURL() {
        // Create and show the streaming dialog
        StreamingDialog dialog = new StreamingDialog();
        dialog.showAndWait().ifPresent(mediaItem -> {
            // Add the media item to the current playlist
            if (currentPlaylist.get() != null) {
                currentPlaylist.get().addItem(mediaItem);

                // Select and play the new item
                int index = currentPlaylist.get().getItems().size() - 1;
                playlistView.getSelectionModel().select(index);
                currentItemIndex = index;
                playMedia(mediaItem);
            }
        });
    }

    @FXML
    private void handleShowKeyboardShortcuts() {
        // Create and show the keyboard shortcuts dialog
        KeyboardShortcutsDialog dialog = new KeyboardShortcutsDialog();
        dialog.showAndWait();
    }

    @FXML
    private void handleMute() {
        if (mediaPlayerComponent.getMediaPlayer() != null) {
            // Toggle mute state
            boolean isMuted = mediaPlayerComponent.getVolume() == 0;

            if (isMuted) {
                // Unmute - restore to previous volume or default to 50%
                mediaPlayerComponent.setVolume(lastVolume > 0 ? lastVolume : 0.5);
                volumeSlider.setValue(mediaPlayerComponent.getVolume());

                // Update mute button icon
                ImageView imageView = (ImageView) muteButton.getGraphic();
                imageView.setImage(new Image(getClass().getResourceAsStream("/com/dev/fplayer/assets/volume.png")));
            } else {
                // Mute - save current volume and set to 0
                lastVolume = mediaPlayerComponent.getVolume();
                mediaPlayerComponent.setVolume(0);
                volumeSlider.setValue(0);

                // Update mute button icon
                ImageView imageView = (ImageView) muteButton.getGraphic();
                imageView.setImage(new Image(getClass().getResourceAsStream("/com/dev/fplayer/assets/mute.png")));
            }
        }
    }

    @FXML
    private void handleVolumeChange() {
        if (mediaPlayerComponent.getMediaPlayer() != null) {
            double volume = volumeSlider.getValue();
            mediaPlayerComponent.setVolume(volume);

            // Update mute button icon based on volume
            ImageView imageView = (ImageView) muteButton.getGraphic();
            if (volume == 0) {
                imageView.setImage(new Image(getClass().getResourceAsStream("/com/dev/fplayer/assets/mute.png")));
            } else {
                imageView.setImage(new Image(getClass().getResourceAsStream("/com/dev/fplayer/assets/volume.png")));
                lastVolume = volume;
            }
        }
    }

    @FXML
    private void handleFullscreen() {
        if (mainPane.getScene() != null && mainPane.getScene().getWindow() != null) {
            Stage stage = (Stage) mainPane.getScene().getWindow();
            stage.setFullScreen(!stage.isFullScreen());

            // Update fullscreen button icon
            ImageView imageView = (ImageView) fullscreenButton.getGraphic();
            if (stage.isFullScreen()) {
                imageView.setImage(
                        new Image(getClass().getResourceAsStream("/com/dev/fplayer/assets/exit-fullscreen.png")));
            } else {
                imageView.setImage(new Image(getClass().getResourceAsStream("/com/dev/fplayer/assets/fullscreen.png")));
            }
        }
    }

    @FXML
    private void handleSettings() {
        // Create and show the settings dialog
        PlayerSettingsDialog dialog = new PlayerSettingsDialog(mediaPlayerComponent);
        dialog.showAndWait();
    }

    @FXML
    private void handleShowVideoDownloader() {
        // Show the video downloader tab
        for (Tab tab : effectsTabPane.getTabs()) {
            if (tab.getText().equals("Video Downloader")) {
                effectsTabPane.getSelectionModel().select(tab);
                break;
            }
        }
    }

    @FXML
    private void handleShowStreamingQuality() {
        // Show the streaming quality tab
        for (Tab tab : effectsTabPane.getTabs()) {
            if (tab.getText().equals("Streaming Quality")) {
                effectsTabPane.getSelectionModel().select(tab);
                break;
            }
        }
    }
}
