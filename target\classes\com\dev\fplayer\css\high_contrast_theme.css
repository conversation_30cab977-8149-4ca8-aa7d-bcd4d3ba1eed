/* High Contrast Theme */
.root {
    -fx-font-family: "Segoe UI", Arial, sans-serif;
    -fx-background-color: black;
    -fx-text-fill: white;
}

/* Menu bar styles */
.menu-bar {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 0 0 1 0;
}

.menu-bar .label {
    -fx-text-fill: white;
}

.menu-item {
    -fx-background-color: black;
}

.menu-item .label {
    -fx-text-fill: white;
}

.menu-item:hover {
    -fx-background-color: white;
}

.menu-item:hover .label {
    -fx-text-fill: black;
}

/* Button styles */
.button {
    -fx-background-color: black;
    -fx-text-fill: white;
    -fx-background-radius: 0;
    -fx-border-color: white;
    -fx-border-width: 1;
}

.button:hover {
    -fx-background-color: white;
    -fx-text-fill: black;
}

.button:pressed {
    -fx-background-color: #808080;
    -fx-text-fill: black;
}

/* Transport control buttons */
#transportControls .button {
    -fx-font-size: 16px;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-background-radius: 0;
    -fx-border-radius: 0;
}

#playButton {
    -fx-background-color: white;
    -fx-text-fill: black;
}

#playButton:hover {
    -fx-background-color: #e0e0e0;
}

/* Slider styles */
.slider .track {
    -fx-background-color: #404040;
}

.slider .thumb {
    -fx-background-color: white;
}

/* List view styles */
.list-view {
    -fx-background-color: black;
    -fx-control-inner-background: black;
    -fx-border-color: white;
    -fx-border-width: 1;
}

.list-cell {
    -fx-background-color: black;
    -fx-text-fill: white;
}

.list-cell:filled:selected {
    -fx-background-color: white;
    -fx-text-fill: black;
}

.list-cell:filled:hover {
    -fx-background-color: #404040;
}

/* Tab pane styles */
.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: black;
}

.tab-pane .tab {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 1;
}

.tab-pane .tab:selected {
    -fx-background-color: white;
}

.tab-pane .tab:selected .tab-label {
    -fx-text-fill: black;
}

.tab .tab-label {
    -fx-text-fill: white;
}

/* Equalizer styles */
.equalizer-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.freq-label {
    -fx-text-fill: white;
    -fx-font-size: 10px;
}

.value-label {
    -fx-text-fill: white;
    -fx-font-size: 10px;
}

.preset-button {
    -fx-background-color: black;
    -fx-text-fill: white;
    -fx-background-radius: 0;
    -fx-border-color: white;
    -fx-border-width: 1;
    -fx-font-size: 11px;
}

.preset-button:hover {
    -fx-background-color: white;
    -fx-text-fill: black;
}

/* Sound effects styles */
.effects-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.effect-button {
    -fx-background-color: black;
    -fx-text-fill: white;
    -fx-background-radius: 0;
    -fx-border-color: white;
    -fx-border-width: 1;
    -fx-font-size: 11px;
}

.effect-button:hover {
    -fx-background-color: white;
    -fx-text-fill: black;
}

/* Split pane styles */
.split-pane {
    -fx-background-color: black;
}

.split-pane-divider {
    -fx-background-color: white;
    -fx-padding: 0 1 0 1;
}

/* Label styles */
.label {
    -fx-text-fill: white;
}

/* Check box styles */
.check-box {
    -fx-text-fill: white;
}

.check-box .box {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 1;
}

.check-box:selected .mark {
    -fx-background-color: black;
}

.check-box:selected .box {
    -fx-background-color: white;
}

/* Dialog styles */
.dialog-pane {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 1;
}

.dialog-pane .label {
    -fx-text-fill: white;
}

.dialog-pane:header .header-panel {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 0 0 1 0;
}

.dialog-pane:header .header-panel .label {
    -fx-text-fill: white;
}

/* Text field styles */
.text-field {
    -fx-background-color: black;
    -fx-text-fill: white;
    -fx-prompt-text-fill: #a0a0a0;
    -fx-border-color: white;
    -fx-border-width: 1;
}

/* Combo box styles */
.combo-box {
    -fx-background-color: black;
    -fx-text-fill: white;
    -fx-border-color: white;
    -fx-border-width: 1;
}

.combo-box .list-cell {
    -fx-text-fill: white;
    -fx-background-color: black;
}

.combo-box-popup .list-view {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 1;
}

/* Scroll bar styles */
.scroll-bar {
    -fx-background-color: black;
}

.scroll-bar .thumb {
    -fx-background-color: white;
}

.scroll-bar .increment-button,
.scroll-bar .decrement-button {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 1;
}

/* Table view styles */
.table-view {
    -fx-background-color: black;
    -fx-table-cell-border-color: white;
    -fx-border-color: white;
    -fx-border-width: 1;
}

.table-view .column-header-background {
    -fx-background-color: black;
}

.table-view .column-header, .table-view .filler {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 0 1 1 0;
}

.table-view .column-header .label {
    -fx-text-fill: white;
}

.table-row-cell {
    -fx-background-color: black;
    -fx-text-fill: white;
    -fx-border-color: white;
    -fx-border-width: 0 0 1 0;
}

.table-row-cell:selected {
    -fx-background-color: white;
    -fx-text-fill: black;
}

.table-row-cell:selected .text {
    -fx-fill: black;
}

/* Visualization styles */
.visualization-container {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 1;
}

/* Tooltip styles */
.tooltip {
    -fx-background-color: white;
    -fx-text-fill: black;
}

/* Progress bar styles */
.progress-bar {
    -fx-background-color: black;
    -fx-border-color: white;
    -fx-border-width: 1;
}

.progress-bar .bar {
    -fx-background-color: white;
}

/* Separator styles */
.separator .line {
    -fx-border-color: white;
    -fx-border-width: 1;
}
