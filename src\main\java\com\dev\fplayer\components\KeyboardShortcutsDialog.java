package com.dev.fplayer.components;

import com.dev.fplayer.utils.KeyboardShortcutManager;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.input.KeyCombination;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Priority;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;

import java.util.Map;

/**
 * Dialog for displaying keyboard shortcuts
 */
public class KeyboardShortcutsDialog extends Dialog<Void> {
    
    /**
     * Create a new keyboard shortcuts dialog
     */
    public KeyboardShortcutsDialog() {
        setTitle("Keyboard Shortcuts");
        setHeaderText("Available Keyboard Shortcuts");
        
        // Set the button types
        ButtonType closeButtonType = new ButtonType("Close", ButtonBar.ButtonData.CANCEL_CLOSE);
        getDialogPane().getButtonTypes().add(closeButtonType);
        
        // Create the grid
        GridPane grid = new GridPane();
        grid.setHgap(20);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));
        
        // Add headers
        Label shortcutLabel = new Label("Shortcut");
        shortcutLabel.setFont(Font.font("System", FontWeight.BOLD, 12));
        Label descriptionLabel = new Label("Description");
        descriptionLabel.setFont(Font.font("System", FontWeight.BOLD, 12));
        
        grid.add(shortcutLabel, 0, 0);
        grid.add(descriptionLabel, 1, 0);
        
        // Get shortcuts from the manager
        KeyboardShortcutManager shortcutManager = KeyboardShortcutManager.getInstance();
        Map<String, KeyCombination> shortcuts = shortcutManager.getShortcuts();
        Map<String, String> descriptions = shortcutManager.getDescriptions();
        
        // Add shortcuts to the grid
        int row = 1;
        for (Map.Entry<String, KeyCombination> entry : shortcuts.entrySet()) {
            String id = entry.getKey();
            KeyCombination combination = entry.getValue();
            String description = descriptions.get(id);
            
            if (combination != null && description != null) {
                Label shortcutText = new Label(combination.toString());
                Label descriptionText = new Label(description);
                
                grid.add(shortcutText, 0, row);
                grid.add(descriptionText, 1, row);
                
                // Make description text wrap
                descriptionText.setWrapText(true);
                GridPane.setHgrow(descriptionText, Priority.ALWAYS);
                
                row++;
            }
        }
        
        // Set the dialog content
        getDialogPane().setContent(grid);
        getDialogPane().setPrefWidth(600);
        getDialogPane().setPrefHeight(500);
    }
}
