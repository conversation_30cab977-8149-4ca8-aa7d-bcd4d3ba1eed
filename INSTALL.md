# FPlayer Installation Guide

## System Requirements

- **Java**: Java 11 or higher
- **Operating System**: Windows 10+, macOS 10.14+, or Linux
- **Memory**: Minimum 512MB RAM (1GB recommended)
- **Storage**: 100MB free space

## Installation Methods

### Method 1: Using Executable Scripts (Recommended)

#### Windows
1. **Using Batch File**:
   - Double-click `FPlayer.bat`
   - The script will automatically check for Java and Maven
   - If <PERSON><PERSON> is available, it will compile and run the application
   - If not, it will try to run from a pre-compiled JAR

2. **Using PowerShell**:
   - Right-click on `FPlayer.ps1` and select "Run with PowerShell"
   - Or open PowerShell and run: `.\FPlayer.ps1`

#### Linux/macOS
1. **Make the script executable**:
   ```bash
   chmod +x FPlayer.sh
   ```

2. **Run the application**:
   ```bash
   ./FPlayer.sh
   ```

### Method 2: Manual Compilation

#### Prerequisites
1. Install Java 11 or higher
2. Install Apache Maven 3.6+

#### Steps
1. **Clone or download the project**
2. **Open terminal/command prompt in the project directory**
3. **Compile the project**:
   ```bash
   mvn clean compile
   ```
4. **Run the application**:
   ```bash
   mvn javafx:run
   ```

### Method 3: Using Pre-compiled JAR

If a JAR file is available in the `target` directory:

```bash
java -jar target/FPlayer-1.0-SNAPSHOT.jar
```

## Troubleshooting

### Common Issues

1. **"Java is not installed or not in PATH"**
   - Download and install Java from [Oracle](https://www.oracle.com/java/technologies/downloads/) or [OpenJDK](https://openjdk.org/)
   - Make sure Java is added to your system PATH

2. **"Maven not found"**
   - Install Apache Maven from [maven.apache.org](https://maven.apache.org/download.cgi)
   - Or use the JAR method if available

3. **Application won't start**
   - Check Java version: `java -version`
   - Ensure you have Java 11 or higher
   - Check console output for error messages

4. **Permission denied (Linux/macOS)**
   - Make sure the script is executable: `chmod +x FPlayer.sh`
   - Check file permissions

### Getting Help

If you encounter issues:
1. Check the console output for error messages
2. Verify all system requirements are met
3. Try running with verbose output: `mvn javafx:run -X`

## Desktop Integration

### Linux
1. Copy `FPlayer.desktop` to `~/.local/share/applications/`
2. Update the `Exec` and `Icon` paths in the file
3. The application will appear in your application menu

### Windows
1. Create a shortcut to `FPlayer.bat`
2. Place it on your desktop or in the Start Menu

### macOS
1. Create an Automator application that runs `./FPlayer.sh`
2. Save it to your Applications folder

## Uninstallation

To remove FPlayer:
1. Delete the project directory
2. Remove any desktop shortcuts or menu entries
3. (Optional) Remove Java if it was installed only for FPlayer
