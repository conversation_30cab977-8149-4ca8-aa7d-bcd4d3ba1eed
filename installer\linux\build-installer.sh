#!/bin/bash
# Build script for FPlayer Linux installer

echo "Building FPlayer Linux installer..."

# Set paths
PROJECT_ROOT=../..
OUTPUT_DIR=$PROJECT_ROOT/target/installer
RESOURCES_DIR=$PROJECT_ROOT/src/main/resources/com/dev/fplayer/assets

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Set variables
APP_VERSION=1.0.0
APP_NAME=FPlayer
APP_ICON=$RESOURCES_DIR/app-icon.png
DESKTOP_FILE=fplayer.desktop

# Check if jpackage has been run
if [ ! -d "$PROJECT_ROOT/target/fplayer" ]; then
    echo "ERROR: Application files not found at $PROJECT_ROOT/target/fplayer"
    echo "Please run 'mvn javafx:jlink jpackage:jpackage' first"
    exit 1
fi

# Create DEB package
echo "Creating DEB package..."
jpackage --type deb \
         --name "$APP_NAME" \
         --app-version "$APP_VERSION" \
         --input "$PROJECT_ROOT/target/fplayer" \
         --main-jar "FPlayer-$APP_VERSION.jar" \
         --main-class "com.dev.fplayer.App" \
         --icon "$APP_ICON" \
         --linux-menu-group "AudioVideo" \
         --linux-shortcut \
         --linux-package-name "fplayer" \
         --linux-deb-maintainer "<EMAIL>" \
         --description "Comprehensive Media Player" \
         --dest "$OUTPUT_DIR"

# Create RPM package
echo "Creating RPM package..."
jpackage --type rpm \
         --name "$APP_NAME" \
         --app-version "$APP_VERSION" \
         --input "$PROJECT_ROOT/target/fplayer" \
         --main-jar "FPlayer-$APP_VERSION.jar" \
         --main-class "com.dev.fplayer.App" \
         --icon "$APP_ICON" \
         --linux-menu-group "AudioVideo" \
         --linux-shortcut \
         --linux-package-name "fplayer" \
         --linux-rpm-license-type "MIT" \
         --description "Comprehensive Media Player" \
         --dest "$OUTPUT_DIR"

if [ $? -eq 0 ]; then
    echo "Installers created successfully in $OUTPUT_DIR"
else
    echo "Error creating installers"
    exit 1
fi

exit 0
