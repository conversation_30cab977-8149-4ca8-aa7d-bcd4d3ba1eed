package com.dev.fplayer;

import com.dev.fplayer.utils.ThemeManager;
import javafx.application.Application;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Simple test application to verify that our changes are working
 */
public class TestApp extends Application {

    @Override
    public void start(Stage stage) {
        // Create a simple UI
        Label label = new Label("FPlayer Test Application");
        Button button = new Button("Test Button");
        
        VBox root = new VBox(10, label, button);
        root.setPadding(new javafx.geometry.Insets(20));
        
        Scene scene = new Scene(root, 300, 200);
        
        // Try to initialize the ThemeManager
        try {
            ThemeManager themeManager = ThemeManager.getInstance();
            themeManager.initialize(scene, "Dark");
            label.setText("ThemeManager initialized successfully!");
        } catch (Exception e) {
            label.setText("Error: " + e.getMessage());
            e.printStackTrace();
        }
        
        stage.setTitle("FPlayer Test");
        stage.setScene(scene);
        stage.show();
    }

    public static void main(String[] args) {
        launch();
    }
}
