package com.dev.fplayer.utils;

import javafx.scene.Scene;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyCodeCombination;
import javafx.scene.input.KeyCombination;
import javafx.scene.input.KeyEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Manages keyboard shortcuts for the application
 */
public class KeyboardShortcutManager {

    private static KeyboardShortcutManager instance;

    private Scene scene;
    private Map<String, KeyCombination> shortcuts;
    private Map<String, Consumer<KeyEvent>> actions;
    private Map<String, String> descriptions;

    /**
     * Get the singleton instance
     * 
     * @return The KeyboardShortcutManager instance
     */
    public static KeyboardShortcutManager getInstance() {
        if (instance == null) {
            instance = new KeyboardShortcutManager();
        }
        return instance;
    }

    /**
     * Private constructor for singleton
     */
    private KeyboardShortcutManager() {
        shortcuts = new HashMap<>();
        actions = new HashMap<>();
        descriptions = new HashMap<>();

        // Register default shortcuts
        registerDefaultShortcuts();
    }

    /**
     * Register default keyboard shortcuts
     */
    private void registerDefaultShortcuts() {
        // Playback controls
        registerShortcut("play", new KeyCodeCombination(KeyCode.SPACE),
                "Play/Pause", "Toggle playback");

        registerShortcut("stop", new KeyCodeCombination(KeyCode.S),
                "Stop", "Stop playback");

        registerShortcut("next", new KeyCodeCombination(KeyCode.RIGHT, KeyCombination.ALT_DOWN),
                "Next", "Play next item");

        registerShortcut("previous", new KeyCodeCombination(KeyCode.LEFT, KeyCombination.ALT_DOWN),
                "Previous", "Play previous item");

        // Volume controls
        registerShortcut("volumeUp", new KeyCodeCombination(KeyCode.UP),
                "Volume Up", "Increase volume");

        registerShortcut("volumeDown", new KeyCodeCombination(KeyCode.DOWN),
                "Volume Down", "Decrease volume");

        registerShortcut("mute", new KeyCodeCombination(KeyCode.M),
                "Mute", "Toggle mute");

        // Seeking
        registerShortcut("seekForward", new KeyCodeCombination(KeyCode.RIGHT),
                "Seek Forward", "Seek forward 10 seconds");

        registerShortcut("seekBackward", new KeyCodeCombination(KeyCode.LEFT),
                "Seek Backward", "Seek backward 10 seconds");

        registerShortcut("seekForwardLarge", new KeyCodeCombination(KeyCode.RIGHT, KeyCombination.CONTROL_DOWN),
                "Seek Forward (Large)", "Seek forward 60 seconds");

        registerShortcut("seekBackwardLarge", new KeyCodeCombination(KeyCode.LEFT, KeyCombination.CONTROL_DOWN),
                "Seek Backward (Large)", "Seek backward 60 seconds");

        // Fullscreen
        registerShortcut("fullscreen", new KeyCodeCombination(KeyCode.F),
                "Fullscreen", "Toggle fullscreen");

        // File operations
        registerShortcut("openFile", new KeyCodeCombination(KeyCode.O, KeyCombination.CONTROL_DOWN),
                "Open File", "Open media file");

        registerShortcut("openURL", new KeyCodeCombination(KeyCode.U, KeyCombination.CONTROL_DOWN),
                "Open URL", "Open media URL");

        // Playlist operations
        registerShortcut("newPlaylist", new KeyCodeCombination(KeyCode.N, KeyCombination.CONTROL_DOWN),
                "New Playlist", "Create new playlist");

        registerShortcut("savePlaylist", new KeyCodeCombination(KeyCode.S, KeyCombination.CONTROL_DOWN),
                "Save Playlist", "Save current playlist");

        // View controls
        registerShortcut("showEqualizer", new KeyCodeCombination(KeyCode.E, KeyCombination.CONTROL_DOWN),
                "Show Equalizer", "Show/hide equalizer");

        registerShortcut("showVisualization", new KeyCodeCombination(KeyCode.V, KeyCombination.CONTROL_DOWN),
                "Show Visualization", "Show/hide visualization");

        // Application controls
        registerShortcut("exit", new KeyCodeCombination(KeyCode.Q, KeyCombination.CONTROL_DOWN),
                "Exit", "Exit application");

        registerShortcut("help", new KeyCodeCombination(KeyCode.F1),
                "Help", "Show help");
    }

    /**
     * Initialize the shortcut manager with a scene
     * 
     * @param scene The scene to manage shortcuts for
     */
    public void initialize(Scene scene) {
        this.scene = scene;

        if (scene != null) {
            // Add key event handler
            scene.addEventFilter(KeyEvent.KEY_PRESSED, this::handleKeyEvent);
        }
    }

    /**
     * Set the scene after it becomes available
     * 
     * @param scene The scene to manage shortcuts for
     */
    public void setScene(Scene scene) {
        if (this.scene == null && scene != null) {
            initialize(scene);
        }
    }

    /**
     * Handle key events
     * 
     * @param event The key event
     */
    private void handleKeyEvent(KeyEvent event) {
        for (Map.Entry<String, KeyCombination> entry : shortcuts.entrySet()) {
            String id = entry.getKey();
            KeyCombination combination = entry.getValue();

            if (combination.match(event)) {
                Consumer<KeyEvent> action = actions.get(id);
                if (action != null) {
                    action.accept(event);
                    event.consume();
                }
            }
        }
    }

    /**
     * Register a keyboard shortcut
     * 
     * @param id          The shortcut ID
     * @param combination The key combination
     * @param name        The shortcut name
     * @param description The shortcut description
     */
    public void registerShortcut(String id, KeyCombination combination, String name, String description) {
        shortcuts.put(id, combination);
        descriptions.put(id, name + ": " + description);
    }

    /**
     * Register an action for a shortcut
     * 
     * @param id     The shortcut ID
     * @param action The action to perform
     */
    public void registerAction(String id, Consumer<KeyEvent> action) {
        actions.put(id, action);
    }

    /**
     * Get all registered shortcuts
     * 
     * @return Map of shortcut IDs to key combinations
     */
    public Map<String, KeyCombination> getShortcuts() {
        return shortcuts;
    }

    /**
     * Get all shortcut descriptions
     * 
     * @return Map of shortcut IDs to descriptions
     */
    public Map<String, String> getDescriptions() {
        return descriptions;
    }

    /**
     * Get the key combination for a shortcut
     * 
     * @param id The shortcut ID
     * @return The key combination, or null if not found
     */
    public KeyCombination getShortcut(String id) {
        return shortcuts.get(id);
    }

    /**
     * Get the description for a shortcut
     * 
     * @param id The shortcut ID
     * @return The description, or null if not found
     */
    public String getDescription(String id) {
        return descriptions.get(id);
    }

    /**
     * Update a shortcut
     * 
     * @param id          The shortcut ID
     * @param combination The new key combination
     */
    public void updateShortcut(String id, KeyCombination combination) {
        if (shortcuts.containsKey(id)) {
            shortcuts.put(id, combination);
        }
    }
}
