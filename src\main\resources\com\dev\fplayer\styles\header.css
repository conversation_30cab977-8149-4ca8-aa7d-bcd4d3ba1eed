/* Enhanced header styling */
.header-container {
    -fx-background-color: linear-gradient(to bottom, #1a1a1a, #121212);
    -fx-padding: 12px 15px;
    -fx-border-color: #333333;
    -fx-border-width: 0 0 1px 0;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.5), 15, 0, 0, 3);
}

.app-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: linear-gradient(to bottom, #ffffff, #e0e0e0);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.7), 8, 0.4, 0, 2);
    -fx-font-family: 'Segoe UI', 'Arial', sans-serif;
}

.app-logo {
    -fx-effect: dropshadow(gaussian, #1db954, 15, 0.5, 0, 0);
    -fx-background-radius: 8px;
}

/* Responsive header for different screen sizes */
@media screen and (max-width: 600px) {
    .header-container {
        -fx-padding: 8px 10px;
    }

    .app-title {
        -fx-font-size: 20px;
    }
}

@media screen and (min-width: 1200px) {
    .header-container {
        -fx-padding: 15px 20px;
    }

    .app-title {
        -fx-font-size: 32px;
    }
}

/* Main toolbar styling */
.main-toolbar {
    -fx-background-color: #252525;
    -fx-padding: 5px 10px;
    -fx-spacing: 8px;
    -fx-border-color: #333333;
    -fx-border-width: 0 0 1px 0;
}

.toolbar-button {
    -fx-background-color: transparent;
    -fx-padding: 5px;
    -fx-cursor: hand;
}

.toolbar-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.6), 5, 0, 0, 0);
}

.toolbar-button:pressed {
    -fx-background-color: rgba(0, 0, 0, 0.2);
}

.toolbar-separator {
    -fx-background-color: #444444;
    -fx-padding: 0 1px;
}

/* Search box styling */
.search-box {
    -fx-background-color: #333333;
    -fx-background-radius: 15px;
    -fx-padding: 5px 10px;
    -fx-text-fill: white;
    -fx-prompt-text-fill: #888888;
    -fx-border-color: #444444;
    -fx-border-radius: 15px;
    -fx-border-width: 1px;
}

.search-box:focused {
    -fx-border-color: #1DB954;
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.4), 5, 0, 0, 0);
}
