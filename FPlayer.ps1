# FPlayer - Media Player Application
# PowerShell Script Launcher

Write-Host "Starting FPlayer..." -ForegroundColor Green
Write-Host ""

# Function to check if command exists
function Test-CommandExists {
    param($command)
    $null = Get-Command $command -ErrorAction SilentlyContinue
    return $?
}

# Check if Java is installed
if (-not (Test-CommandExists "java")) {
    Write-Host "Error: Java is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Java 11 or higher to run FPlayer" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Display Java version
Write-Host "Java version:" -ForegroundColor Cyan
java -version
Write-Host ""

# Check if Maven is available
if (Test-CommandExists "mvn") {
    Write-Host "Using Maven to run FPlayer..." -ForegroundColor Cyan
    & mvn clean javafx:run
    $exitCode = $LASTEXITCODE
} else {
    # Try to run with compiled JAR
    if (Test-Path "target\FPlayer-1.0-SNAPSHOT.jar") {
        Write-Host "Running FPlayer from JAR..." -ForegroundColor Cyan
        & java -jar target\FPlayer-1.0-SNAPSHOT.jar
        $exitCode = $LASTEXITCODE
    } else {
        Write-Host "Error: Neither Maven nor compiled JAR found" -ForegroundColor Red
        Write-Host "Please compile the project first using: mvn clean compile" -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit 1
    }
}

if ($exitCode -ne 0) {
    Write-Host ""
    Write-Host "FPlayer encountered an error during execution" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit $exitCode
}

Write-Host "FPlayer finished successfully" -ForegroundColor Green
