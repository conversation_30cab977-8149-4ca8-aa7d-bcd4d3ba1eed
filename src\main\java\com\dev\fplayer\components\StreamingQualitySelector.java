package com.dev.fplayer.components;

import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;

/**
 * Component for selecting streaming quality
 */
public class StreamingQualitySelector extends VBox {
    
    private final ComboBox<String> qualityComboBox;
    private final MediaPlayerComponent mediaPlayerComponent;
    
    /**
     * Create a new streaming quality selector
     * @param mediaPlayerComponent The media player component to control
     */
    public StreamingQualitySelector(MediaPlayerComponent mediaPlayerComponent) {
        this.mediaPlayerComponent = mediaPlayerComponent;
        
        setPadding(new Insets(10));
        setSpacing(10);
        setAlignment(Pos.CENTER);
        
        // Create the quality selection controls
        Label titleLabel = new Label("Streaming Quality");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");
        
        HBox qualityBox = new HBox(10);
        qualityBox.setAlignment(Pos.CENTER);
        Label qualityLabel = new Label("Quality:");
        qualityComboBox = new ComboBox<>();
        qualityComboBox.getItems().addAll("Auto", "1080p", "720p", "480p", "360p", "240p");
        qualityComboBox.setValue("Auto");
        qualityComboBox.setPrefWidth(150);
        qualityComboBox.setOnAction(e -> applyQualitySettings());
        qualityBox.getChildren().addAll(qualityLabel, qualityComboBox);
        
        // Create the auto-adjust checkbox
        CheckBox autoAdjustCheckBox = new CheckBox("Auto-adjust based on network speed");
        autoAdjustCheckBox.setSelected(true);
        autoAdjustCheckBox.setOnAction(e -> {
            boolean autoAdjust = autoAdjustCheckBox.isSelected();
            qualityComboBox.setDisable(autoAdjust);
            if (autoAdjust) {
                qualityComboBox.setValue("Auto");
                applyQualitySettings();
            }
        });
        
        // Create the bandwidth usage controls
        TitledPane bandwidthPane = new TitledPane();
        bandwidthPane.setText("Bandwidth Usage");
        bandwidthPane.setCollapsible(true);
        
        VBox bandwidthBox = new VBox(10);
        bandwidthBox.setPadding(new Insets(10));
        
        // Data saver mode
        RadioButton dataSaverRadio = new RadioButton("Data Saver (Low quality, less data)");
        RadioButton balancedRadio = new RadioButton("Balanced (Medium quality)");
        RadioButton highQualityRadio = new RadioButton("High Quality (Best quality, more data)");
        
        ToggleGroup bandwidthGroup = new ToggleGroup();
        dataSaverRadio.setToggleGroup(bandwidthGroup);
        balancedRadio.setToggleGroup(bandwidthGroup);
        highQualityRadio.setToggleGroup(bandwidthGroup);
        
        balancedRadio.setSelected(true);
        
        bandwidthGroup.selectedToggleProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal == dataSaverRadio) {
                qualityComboBox.setValue("360p");
            } else if (newVal == balancedRadio) {
                qualityComboBox.setValue("720p");
            } else if (newVal == highQualityRadio) {
                qualityComboBox.setValue("1080p");
            }
            applyQualitySettings();
        });
        
        bandwidthBox.getChildren().addAll(dataSaverRadio, balancedRadio, highQualityRadio);
        bandwidthPane.setContent(bandwidthBox);
        
        // Create the buffer settings
        TitledPane bufferPane = new TitledPane();
        bufferPane.setText("Buffer Settings");
        bufferPane.setCollapsible(true);
        
        VBox bufferBox = new VBox(10);
        bufferBox.setPadding(new Insets(10));
        
        Label bufferSizeLabel = new Label("Buffer Size:");
        Slider bufferSizeSlider = new Slider(1, 60, 10);
        bufferSizeSlider.setShowTickLabels(true);
        bufferSizeSlider.setShowTickMarks(true);
        bufferSizeSlider.setMajorTickUnit(10);
        bufferSizeSlider.setBlockIncrement(1);
        
        Label bufferSizeValueLabel = new Label("10 seconds");
        bufferSizeSlider.valueProperty().addListener((obs, oldVal, newVal) -> {
            int value = newVal.intValue();
            bufferSizeValueLabel.setText(value + " seconds");
        });
        
        bufferBox.getChildren().addAll(bufferSizeLabel, bufferSizeSlider, bufferSizeValueLabel);
        bufferPane.setContent(bufferBox);
        
        // Add all controls to the component
        getChildren().addAll(titleLabel, qualityBox, autoAdjustCheckBox, bandwidthPane, bufferPane);
    }
    
    /**
     * Apply the selected quality settings to the media player
     */
    private void applyQualitySettings() {
        String quality = qualityComboBox.getValue();
        
        // In a real implementation, this would adjust the streaming quality
        // For this example, we'll just print the selected quality
        System.out.println("Streaming quality set to: " + quality);
        
        // If we had a real streaming player, we would apply the quality setting here
        // For example:
        // mediaPlayerComponent.setStreamingQuality(quality);
    }
}
