package com.dev.fplayer;

import javafx.application.Preloader;
import javafx.application.Preloader.StateChangeNotification.Type;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressBar;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;
import javafx.stage.StageStyle;

/**
 * Splash screen shown during application startup
 */
public class SplashScreen extends Preloader {
    
    private Stage splashStage;
    private ProgressBar progressBar;
    private Label progressLabel;
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        this.splashStage = primaryStage;
        
        // Create the splash screen UI
        VBox root = new VBox(20);
        root.setAlignment(Pos.CENTER);
        root.setStyle("-fx-background-color: #1e1e1e;");
        
        // Logo
        ImageView logoView = new ImageView(new Image(getClass().getResourceAsStream("/com/dev/fplayer/assets/logo.png")));
        logoView.setFitWidth(200);
        logoView.setPreserveRatio(true);
        
        // App name
        Label nameLabel = new Label("FPlayer");
        nameLabel.setFont(Font.font("System", 28));
        nameLabel.setStyle("-fx-text-fill: white;");
        
        // Version
        Label versionLabel = new Label("Version 1.0");
        versionLabel.setFont(Font.font("System", 14));
        versionLabel.setStyle("-fx-text-fill: #cccccc;");
        
        // Progress bar
        progressBar = new ProgressBar(0);
        progressBar.setPrefWidth(300);
        
        // Progress label
        progressLabel = new Label("Loading...");
        progressLabel.setStyle("-fx-text-fill: white;");
        
        root.getChildren().addAll(logoView, nameLabel, versionLabel, progressBar, progressLabel);
        
        // Create the scene
        Scene scene = new Scene(root, 400, 300);
        
        // Configure the stage
        splashStage.initStyle(StageStyle.UNDECORATED);
        splashStage.setScene(scene);
        splashStage.getIcons().add(new Image(getClass().getResourceAsStream("/com/dev/fplayer/assets/app-icon.png")));
        splashStage.show();
    }
    
    @Override
    public void handleApplicationNotification(PreloaderNotification info) {
        if (info instanceof ProgressNotification) {
            ProgressNotification progressInfo = (ProgressNotification) info;
            double progress = progressInfo.getProgress();
            
            progressBar.setProgress(progress);
            
            if (progress < 0.2) {
                progressLabel.setText("Initializing application...");
            } else if (progress < 0.4) {
                progressLabel.setText("Loading resources...");
            } else if (progress < 0.6) {
                progressLabel.setText("Loading components...");
            } else if (progress < 0.8) {
                progressLabel.setText("Preparing user interface...");
            } else {
                progressLabel.setText("Starting application...");
            }
        }
    }
    
    @Override
    public void handleStateChangeNotification(StateChangeNotification info) {
        if (info.getType() == Type.BEFORE_START) {
            splashStage.hide();
        }
    }
}
