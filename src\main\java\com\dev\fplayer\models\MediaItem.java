package com.dev.fplayer.models;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * Represents a media item (audio or video) in the player
 */
public class MediaItem {
    
    private String title;
    private String artist;
    private String album;
    private String genre;
    private int year;
    private long duration; // in milliseconds
    private File file;
    private String url;
    private MediaType type;
    private Map<String, Object> metadata;
    
    public enum MediaType {
        AUDIO,
        VIDEO,
        UNKNOWN
    }
    
    /**
     * Create a media item from a file
     * @param file The media file
     */
    public MediaItem(File file) {
        this.file = file;
        this.title = file.getName();
        this.type = determineType(file);
        this.metadata = new HashMap<>();
    }
    
    /**
     * Create a media item from a URL
     * @param url The media URL
     * @param title The title of the media
     * @param type The type of media
     */
    public MediaItem(String url, String title, MediaType type) {
        this.url = url;
        this.title = title;
        this.type = type;
        this.metadata = new HashMap<>();
    }
    
    /**
     * Determine the type of media based on file extension
     * @param file The media file
     * @return The media type
     */
    private MediaType determineType(File file) {
        String name = file.getName().toLowerCase();
        if (name.endsWith(".mp3") || name.endsWith(".wav") || name.endsWith(".aac") || 
            name.endsWith(".flac") || name.endsWith(".ogg") || name.endsWith(".m4a")) {
            return MediaType.AUDIO;
        } else if (name.endsWith(".mp4") || name.endsWith(".avi") || name.endsWith(".mkv") || 
                   name.endsWith(".mov") || name.endsWith(".wmv") || name.endsWith(".flv")) {
            return MediaType.VIDEO;
        } else {
            return MediaType.UNKNOWN;
        }
    }
    
    // Getters and setters
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getArtist() {
        return artist;
    }
    
    public void setArtist(String artist) {
        this.artist = artist;
    }
    
    public String getAlbum() {
        return album;
    }
    
    public void setAlbum(String album) {
        this.album = album;
    }
    
    public String getGenre() {
        return genre;
    }
    
    public void setGenre(String genre) {
        this.genre = genre;
    }
    
    public int getYear() {
        return year;
    }
    
    public void setYear(int year) {
        this.year = year;
    }
    
    public long getDuration() {
        return duration;
    }
    
    public void setDuration(long duration) {
        this.duration = duration;
    }
    
    public File getFile() {
        return file;
    }
    
    public String getUrl() {
        return url;
    }
    
    public MediaType getType() {
        return type;
    }
    
    public void setType(MediaType type) {
        this.type = type;
    }
    
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
    
    public void addMetadata(String key, Object value) {
        this.metadata.put(key, value);
    }
    
    public Object getMetadataValue(String key) {
        return this.metadata.get(key);
    }
    
    @Override
    public String toString() {
        return title + (artist != null ? " - " + artist : "");
    }
}
