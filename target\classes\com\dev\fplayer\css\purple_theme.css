/* Purple Theme */
.root {
    -fx-font-family: "Segoe UI", Arial, sans-serif;
    -fx-background-color: #2d1b4e;
    -fx-text-fill: white;
}

/* Menu bar styles */
.menu-bar {
    -fx-background-color: #1a0f2e;
}

.menu-bar .label {
    -fx-text-fill: white;
}

.menu-item {
    -fx-background-color: #1a0f2e;
}

.menu-item .label {
    -fx-text-fill: white;
}

.menu-item:hover {
    -fx-background-color: #4a2b7e;
}

/* Button styles */
.button {
    -fx-background-color: #4a2b7e;
    -fx-text-fill: white;
    -fx-background-radius: 3;
}

.button:hover {
    -fx-background-color: #5a3b8e;
}

.button:pressed {
    -fx-background-color: #6a4b9e;
}

/* Transport control buttons */
#transportControls .button {
    -fx-font-size: 16px;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-background-radius: 20;
}

/* Slider styles */
.slider .track {
    -fx-background-color: #3a1b6e;
}

.slider .thumb {
    -fx-background-color: #8a5bce;
}

/* Tab pane styles */
.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: #2d1b4e;
}

.tab-pane .tab {
    -fx-background-color: #3a1b6e;
}

.tab-pane .tab:selected {
    -fx-background-color: #4a2b7e;
}

.tab-pane .tab .tab-label {
    -fx-text-fill: white;
}

/* List view styles */
.list-view {
    -fx-background-color: #3a1b6e;
}

.list-view .list-cell {
    -fx-background-color: #3a1b6e;
    -fx-text-fill: white;
}

.list-view .list-cell:filled:selected {
    -fx-background-color: #8a5bce;
}

.list-view .list-cell:filled:hover {
    -fx-background-color: #5a3b8e;
}

/* Split pane styles */
.split-pane {
    -fx-background-color: #2d1b4e;
}

.split-pane .split-pane-divider {
    -fx-background-color: #1a0f2e;
    -fx-padding: 0 1 0 1;
}

/* Scroll bar styles */
.scroll-bar {
    -fx-background-color: #3a1b6e;
}

.scroll-bar .thumb {
    -fx-background-color: #5a3b8e;
}

.scroll-bar .increment-button,
.scroll-bar .decrement-button {
    -fx-background-color: #3a1b6e;
}

/* Visualization component styles */
.audio-visualization {
    -fx-background-color: #1a0f2e;
}

/* Equalizer component styles */
.equalizer-title {
    -fx-font-size: 16px;
    -fx-text-fill: white;
}

/* Sound effects component styles */
.effects-title {
    -fx-font-size: 16px;
    -fx-text-fill: white;
}

/* Tooltip styles */
.tooltip {
    -fx-background-color: #1a0f2e;
    -fx-text-fill: white;
}
