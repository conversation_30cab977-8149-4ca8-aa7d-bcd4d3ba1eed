/* Green Theme */
.root {
    -fx-font-family: "Segoe UI", Arial, sans-serif;
    -fx-background-color: #1e3b2c;
    -fx-text-fill: white;
}

/* Menu bar styles */
.menu-bar {
    -fx-background-color: #0f1f17;
}

.menu-bar .label {
    -fx-text-fill: white;
}

.menu-item {
    -fx-background-color: #0f1f17;
}

.menu-item .label {
    -fx-text-fill: white;
}

.menu-item:hover {
    -fx-background-color: #2a5b3c;
}

/* Button styles */
.button {
    -fx-background-color: #2a5b3c;
    -fx-text-fill: white;
    -fx-background-radius: 3;
}

.button:hover {
    -fx-background-color: #3a6b4c;
}

.button:pressed {
    -fx-background-color: #4a7b5c;
}

/* Transport control buttons */
#transportControls .button {
    -fx-font-size: 16px;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-background-radius: 20;
}

/* Slider styles */
.slider .track {
    -fx-background-color: #1a4b2c;
}

.slider .thumb {
    -fx-background-color: #5a9b6c;
}

/* Tab pane styles */
.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: #1e3b2c;
}

.tab-pane .tab {
    -fx-background-color: #1a4b2c;
}

.tab-pane .tab:selected {
    -fx-background-color: #2a5b3c;
}

.tab-pane .tab .tab-label {
    -fx-text-fill: white;
}

/* List view styles */
.list-view {
    -fx-background-color: #1a4b2c;
}

.list-view .list-cell {
    -fx-background-color: #1a4b2c;
    -fx-text-fill: white;
}

.list-view .list-cell:filled:selected {
    -fx-background-color: #5a9b6c;
}

.list-view .list-cell:filled:hover {
    -fx-background-color: #3a6b4c;
}

/* Split pane styles */
.split-pane {
    -fx-background-color: #1e3b2c;
}

.split-pane .split-pane-divider {
    -fx-background-color: #0f1f17;
    -fx-padding: 0 1 0 1;
}

/* Scroll bar styles */
.scroll-bar {
    -fx-background-color: #1a4b2c;
}

.scroll-bar .thumb {
    -fx-background-color: #3a6b4c;
}

.scroll-bar .increment-button,
.scroll-bar .decrement-button {
    -fx-background-color: #1a4b2c;
}

/* Visualization component styles */
.audio-visualization {
    -fx-background-color: #0f1f17;
}

/* Equalizer component styles */
.equalizer-title {
    -fx-font-size: 16px;
    -fx-text-fill: white;
}

/* Sound effects component styles */
.effects-title {
    -fx-font-size: 16px;
    -fx-text-fill: white;
}

/* Tooltip styles */
.tooltip {
    -fx-background-color: #0f1f17;
    -fx-text-fill: white;
}
