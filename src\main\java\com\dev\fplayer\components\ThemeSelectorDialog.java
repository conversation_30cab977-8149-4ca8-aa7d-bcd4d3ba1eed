package com.dev.fplayer.components;

import com.dev.fplayer.utils.ThemeManager;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;

/**
 * Dialog for selecting and applying themes
 */
public class ThemeSelectorDialog extends Dialog<String> {
    
    private ComboBox<String> themeComboBox;
    
    /**
     * Create a new theme selector dialog
     */
    public ThemeSelectorDialog() {
        // Set up dialog
        setTitle("Select Theme");
        setHeaderText("Choose a theme for the player");
        
        // Set the button types
        ButtonType applyButtonType = new ButtonType("Apply", ButtonBar.ButtonData.OK_DONE);
        getDialogPane().getButtonTypes().addAll(applyButtonType, ButtonType.CANCEL);
        
        // Create the form grid
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 10, 10, 10));
        
        // Create theme selector
        Label themeLabel = new Label("Theme:");
        themeComboBox = new ComboBox<>();
        themeComboBox.getItems().addAll(ThemeManager.getInstance().getThemes().keySet());
        themeComboBox.setValue(ThemeManager.getInstance().getCurrentTheme());
        themeComboBox.setPrefWidth(200);
        
        // Add preview section
        Label previewLabel = new Label("Preview:");
        VBox previewBox = new VBox(10);
        previewBox.setPrefHeight(100);
        previewBox.setPrefWidth(300);
        previewBox.getStyleClass().add("preview-box");
        
        // Add controls to grid
        grid.add(themeLabel, 0, 0);
        grid.add(themeComboBox, 1, 0);
        grid.add(previewLabel, 0, 1);
        grid.add(previewBox, 0, 2, 2, 1);
        
        // Update preview when theme changes
        themeComboBox.valueProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                updatePreview(previewBox, newVal);
            }
        });
        
        // Initialize preview
        updatePreview(previewBox, themeComboBox.getValue());
        
        // Set the content
        getDialogPane().setContent(grid);
        
        // Convert the result to a theme name when the apply button is clicked
        setResultConverter(dialogButton -> {
            if (dialogButton == applyButtonType) {
                return themeComboBox.getValue();
            }
            return null;
        });
    }
    
    /**
     * Update the preview box with the selected theme
     * @param previewBox The preview box
     * @param themeName The theme name
     */
    private void updatePreview(VBox previewBox, String themeName) {
        // Clear existing style classes
        previewBox.getStyleClass().removeAll("dark-preview", "light-preview", "blue-preview", 
                                            "purple-preview", "green-preview", "high-contrast-preview");
        
        // Add the appropriate style class
        switch (themeName) {
            case "Dark":
                previewBox.getStyleClass().add("dark-preview");
                break;
            case "Light":
                previewBox.getStyleClass().add("light-preview");
                break;
            case "Blue":
                previewBox.getStyleClass().add("blue-preview");
                break;
            case "Purple":
                previewBox.getStyleClass().add("purple-preview");
                break;
            case "Green":
                previewBox.getStyleClass().add("green-preview");
                break;
            case "High Contrast":
                previewBox.getStyleClass().add("high-contrast-preview");
                break;
        }
    }
}
