package com.dev.fplayer;

import com.dev.fplayer.models.MediaItem;
import com.dev.fplayer.models.Playlist;
import com.dev.fplayer.utils.ThemeManager;

/**
 * Simple test class to verify that our module structure is correct
 */
public class SimpleTest {
    
    public static void main(String[] args) {
        System.out.println("Starting SimpleTest...");
        
        try {
            // Test ThemeManager
            ThemeManager themeManager = ThemeManager.getInstance();
            System.out.println("ThemeManager initialized successfully!");
            System.out.println("Available themes: " + themeManager.getThemes().keySet());
            
            // Test MediaItem
            MediaItem mediaItem = new MediaItem("http://example.com/test.mp3", "Test Song", MediaItem.MediaType.AUDIO);
            System.out.println("MediaItem created successfully: " + mediaItem);
            
            // Test Playlist
            Playlist playlist = new Playlist("Test Playlist");
            playlist.addItem(mediaItem);
            System.out.println("Playlist created successfully: " + playlist.getName());
            System.out.println("Playlist items: " + playlist.getItems().size());
            
            System.out.println("All tests passed!");
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
