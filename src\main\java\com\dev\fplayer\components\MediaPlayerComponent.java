package com.dev.fplayer.components;

import javafx.beans.property.DoubleProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.scene.layout.BorderPane;
import javafx.scene.media.Media;
import javafx.scene.media.MediaPlayer;
import javafx.scene.media.MediaView;
import javafx.util.Duration;

import java.io.File;
import java.net.URI;

/**
 * Core component for media playback functionality
 */
public class MediaPlayerComponent extends BorderPane {

    private final ObjectProperty<MediaPlayer> mediaPlayerProperty = new SimpleObjectProperty<>(null);
    private MediaView mediaView;
    private final DoubleProperty volumeProperty = new SimpleDoubleProperty(1.0);
    private final DoubleProperty rateProperty = new SimpleDoubleProperty(1.0);
    private final DoubleProperty balanceProperty = new SimpleDoubleProperty(0.0);

    public MediaPlayerComponent() {
        mediaView = new MediaView();
        setCenter(mediaView);

        // Bind the mediaView size to the parent container
        mediaView.fitWidthProperty().bind(widthProperty());
        mediaView.fitHeightProperty().bind(heightProperty());
        mediaView.setPreserveRatio(true);
    }

    /**
     * Load and play media from a file
     *
     * @param file The media file to play
     */
    public void loadMedia(File file) {
        // Dispose of any existing player
        dispose();

        try {
            URI uri = file.toURI();
            Media media = new Media(uri.toString());
            MediaPlayer player = new MediaPlayer(media);
            mediaView.setMediaPlayer(player);

            // Bind properties
            player.volumeProperty().bind(volumeProperty);
            player.rateProperty().bind(rateProperty);
            player.balanceProperty().bind(balanceProperty);

            // Auto-play when loaded
            player.setOnReady(() -> {
                player.play();
            });

            // Set the player in the property
            mediaPlayerProperty.set(player);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Load and play media from a URL
     *
     * @param url The URL of the media to play
     */
    public void loadMedia(String url) {
        // Dispose of any existing player
        dispose();

        try {
            Media media = new Media(url);
            MediaPlayer player = new MediaPlayer(media);
            mediaView.setMediaPlayer(player);

            // Bind properties
            player.volumeProperty().bind(volumeProperty);
            player.rateProperty().bind(rateProperty);
            player.balanceProperty().bind(balanceProperty);

            // Auto-play when loaded
            player.setOnReady(() -> {
                player.play();
            });

            // Set the player in the property
            mediaPlayerProperty.set(player);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // Media control methods

    public void play() {
        MediaPlayer player = mediaPlayerProperty.get();
        if (player != null) {
            player.play();
        }
    }

    public void pause() {
        MediaPlayer player = mediaPlayerProperty.get();
        if (player != null) {
            player.pause();
        }
    }

    public void stop() {
        MediaPlayer player = mediaPlayerProperty.get();
        if (player != null) {
            player.stop();
        }
    }

    public void seek(Duration duration) {
        MediaPlayer player = mediaPlayerProperty.get();
        if (player != null) {
            player.seek(duration);
        }
    }

    // Property getters and setters

    public double getVolume() {
        return volumeProperty.get();
    }

    public void setVolume(double volume) {
        volumeProperty.set(volume);
    }

    public DoubleProperty volumeProperty() {
        return volumeProperty;
    }

    public double getRate() {
        return rateProperty.get();
    }

    public void setRate(double rate) {
        rateProperty.set(rate);
    }

    public DoubleProperty rateProperty() {
        return rateProperty;
    }

    public double getBalance() {
        return balanceProperty.get();
    }

    public void setBalance(double balance) {
        balanceProperty.set(balance);
    }

    public DoubleProperty balanceProperty() {
        return balanceProperty;
    }

    public MediaPlayer getMediaPlayer() {
        return mediaPlayerProperty.get();
    }

    public ObjectProperty<MediaPlayer> mediaPlayerProperty() {
        return mediaPlayerProperty;
    }

    public MediaView getMediaView() {
        return mediaView;
    }

    /**
     * Dispose of resources when no longer needed
     */
    public void dispose() {
        MediaPlayer player = mediaPlayerProperty.get();
        if (player != null) {
            player.dispose();
            mediaPlayerProperty.set(null);
        }
    }
}
