package com.dev.fplayer.components;

import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.scene.media.MediaPlayer;

/**
 * Dialog for configuring player settings
 */
public class PlayerSettingsDialog extends Dialog<Void> {
    
    private final MediaPlayerComponent mediaPlayerComponent;
    
    // Video quality settings
    private Slider brightnessSlider;
    private Slider contrastSlider;
    private Slider saturationSlider;
    private ComboBox<String> aspectRatioComboBox;
    private ComboBox<String> resolutionComboBox;
    
    // Audio quality settings
    private Slider volumeBoostSlider;
    private CheckBox audioNormalizationCheckBox;
    private ComboBox<String> audioQualityComboBox;
    
    /**
     * Create a new player settings dialog
     * @param mediaPlayerComponent The media player component to configure
     */
    public PlayerSettingsDialog(MediaPlayerComponent mediaPlayerComponent) {
        this.mediaPlayerComponent = mediaPlayerComponent;
        
        setTitle("Player Settings");
        setHeaderText("Configure player settings");
        
        // Set the button types
        ButtonType okButtonType = new ButtonType("Apply", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancel", ButtonBar.ButtonData.CANCEL_CLOSE);
        getDialogPane().getButtonTypes().addAll(okButtonType, cancelButtonType);
        
        // Create the content
        TabPane tabPane = new TabPane();
        tabPane.setTabClosingPolicy(TabPane.TabClosingPolicy.UNAVAILABLE);
        
        // Video settings tab
        Tab videoTab = new Tab("Video");
        videoTab.setContent(createVideoSettingsPane());
        tabPane.getTabs().add(videoTab);
        
        // Audio settings tab
        Tab audioTab = new Tab("Audio");
        audioTab.setContent(createAudioSettingsPane());
        tabPane.getTabs().add(audioTab);
        
        // Set the dialog content
        getDialogPane().setContent(tabPane);
        getDialogPane().setPrefWidth(500);
        getDialogPane().setPrefHeight(400);
        
        // Handle dialog result
        setResultConverter(dialogButton -> {
            if (dialogButton == okButtonType) {
                applySettings();
            }
            return null;
        });
    }
    
    /**
     * Create the video settings pane
     * @return The video settings pane
     */
    private VBox createVideoSettingsPane() {
        VBox pane = new VBox(10);
        pane.setPadding(new Insets(20));
        
        // Video quality settings
        TitledPane videoQualityPane = new TitledPane();
        videoQualityPane.setText("Video Quality");
        videoQualityPane.setCollapsible(false);
        
        GridPane videoQualityGrid = new GridPane();
        videoQualityGrid.setHgap(10);
        videoQualityGrid.setVgap(10);
        videoQualityGrid.setPadding(new Insets(10));
        
        // Resolution
        Label resolutionLabel = new Label("Resolution:");
        resolutionComboBox = new ComboBox<>();
        resolutionComboBox.getItems().addAll("Auto", "1080p", "720p", "480p", "360p");
        resolutionComboBox.setValue("Auto");
        resolutionComboBox.setMaxWidth(Double.MAX_VALUE);
        
        // Aspect ratio
        Label aspectRatioLabel = new Label("Aspect Ratio:");
        aspectRatioComboBox = new ComboBox<>();
        aspectRatioComboBox.getItems().addAll("Auto", "16:9", "4:3", "21:9", "1:1");
        aspectRatioComboBox.setValue("Auto");
        aspectRatioComboBox.setMaxWidth(Double.MAX_VALUE);
        
        videoQualityGrid.add(resolutionLabel, 0, 0);
        videoQualityGrid.add(resolutionComboBox, 1, 0);
        videoQualityGrid.add(aspectRatioLabel, 0, 1);
        videoQualityGrid.add(aspectRatioComboBox, 1, 1);
        
        videoQualityPane.setContent(videoQualityGrid);
        
        // Video enhancement settings
        TitledPane videoEnhancementPane = new TitledPane();
        videoEnhancementPane.setText("Video Enhancement");
        videoEnhancementPane.setCollapsible(false);
        
        GridPane videoEnhancementGrid = new GridPane();
        videoEnhancementGrid.setHgap(10);
        videoEnhancementGrid.setVgap(10);
        videoEnhancementGrid.setPadding(new Insets(10));
        
        // Brightness
        Label brightnessLabel = new Label("Brightness:");
        brightnessSlider = new Slider(0, 2, 1);
        brightnessSlider.setShowTickLabels(true);
        brightnessSlider.setShowTickMarks(true);
        brightnessSlider.setMajorTickUnit(0.5);
        brightnessSlider.setBlockIncrement(0.1);
        
        // Contrast
        Label contrastLabel = new Label("Contrast:");
        contrastSlider = new Slider(0, 2, 1);
        contrastSlider.setShowTickLabels(true);
        contrastSlider.setShowTickMarks(true);
        contrastSlider.setMajorTickUnit(0.5);
        contrastSlider.setBlockIncrement(0.1);
        
        // Saturation
        Label saturationLabel = new Label("Saturation:");
        saturationSlider = new Slider(0, 2, 1);
        saturationSlider.setShowTickLabels(true);
        saturationSlider.setShowTickMarks(true);
        saturationSlider.setMajorTickUnit(0.5);
        saturationSlider.setBlockIncrement(0.1);
        
        videoEnhancementGrid.add(brightnessLabel, 0, 0);
        videoEnhancementGrid.add(brightnessSlider, 1, 0);
        videoEnhancementGrid.add(contrastLabel, 0, 1);
        videoEnhancementGrid.add(contrastSlider, 1, 1);
        videoEnhancementGrid.add(saturationLabel, 0, 2);
        videoEnhancementGrid.add(saturationSlider, 1, 2);
        
        videoEnhancementPane.setContent(videoEnhancementGrid);
        
        pane.getChildren().addAll(videoQualityPane, videoEnhancementPane);
        return pane;
    }
    
    /**
     * Create the audio settings pane
     * @return The audio settings pane
     */
    private VBox createAudioSettingsPane() {
        VBox pane = new VBox(10);
        pane.setPadding(new Insets(20));
        
        // Audio quality settings
        TitledPane audioQualityPane = new TitledPane();
        audioQualityPane.setText("Audio Quality");
        audioQualityPane.setCollapsible(false);
        
        GridPane audioQualityGrid = new GridPane();
        audioQualityGrid.setHgap(10);
        audioQualityGrid.setVgap(10);
        audioQualityGrid.setPadding(new Insets(10));
        
        // Audio quality
        Label audioQualityLabel = new Label("Audio Quality:");
        audioQualityComboBox = new ComboBox<>();
        audioQualityComboBox.getItems().addAll("Auto", "High (320 kbps)", "Medium (192 kbps)", "Low (128 kbps)");
        audioQualityComboBox.setValue("Auto");
        audioQualityComboBox.setMaxWidth(Double.MAX_VALUE);
        
        // Audio normalization
        audioNormalizationCheckBox = new CheckBox("Enable Audio Normalization");
        audioNormalizationCheckBox.setSelected(false);
        
        audioQualityGrid.add(audioQualityLabel, 0, 0);
        audioQualityGrid.add(audioQualityComboBox, 1, 0);
        audioQualityGrid.add(audioNormalizationCheckBox, 0, 1, 2, 1);
        
        audioQualityPane.setContent(audioQualityGrid);
        
        // Audio enhancement settings
        TitledPane audioEnhancementPane = new TitledPane();
        audioEnhancementPane.setText("Audio Enhancement");
        audioEnhancementPane.setCollapsible(false);
        
        GridPane audioEnhancementGrid = new GridPane();
        audioEnhancementGrid.setHgap(10);
        audioEnhancementGrid.setVgap(10);
        audioEnhancementGrid.setPadding(new Insets(10));
        
        // Volume boost
        Label volumeBoostLabel = new Label("Volume Boost:");
        volumeBoostSlider = new Slider(0, 2, 1);
        volumeBoostSlider.setShowTickLabels(true);
        volumeBoostSlider.setShowTickMarks(true);
        volumeBoostSlider.setMajorTickUnit(0.5);
        volumeBoostSlider.setBlockIncrement(0.1);
        
        audioEnhancementGrid.add(volumeBoostLabel, 0, 0);
        audioEnhancementGrid.add(volumeBoostSlider, 1, 0);
        
        audioEnhancementPane.setContent(audioEnhancementGrid);
        
        pane.getChildren().addAll(audioQualityPane, audioEnhancementPane);
        return pane;
    }
    
    /**
     * Apply the settings to the media player
     */
    private void applySettings() {
        MediaPlayer player = mediaPlayerComponent.getMediaPlayer();
        if (player == null) {
            return;
        }
        
        // Apply video settings
        // Note: These would typically be applied through CSS or other means
        // as JavaFX MediaPlayer doesn't directly support these adjustments
        
        // Apply audio settings
        if (audioNormalizationCheckBox.isSelected()) {
            // Enable audio normalization
            // This would typically be implemented through audio processing
        }
        
        // Apply volume boost
        double volumeBoost = volumeBoostSlider.getValue();
        if (volumeBoost != 1.0) {
            // Apply volume boost
            mediaPlayerComponent.setVolume(mediaPlayerComponent.getVolume() * volumeBoost);
        }
    }
}
