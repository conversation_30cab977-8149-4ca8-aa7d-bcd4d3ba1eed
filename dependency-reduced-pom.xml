<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.dev</groupId>
  <artifactId>FPlayer</artifactId>
  <version>1.0-SNAPSHOT</version>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.0</version>
        <configuration>
          <release>11</release>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <transformers>
                <transformer>
                  <mainClass>com.dev.fplayer.App</mainClass>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-maven-plugin</artifactId>
        <version>0.0.4</version>
        <executions>
          <execution>
            <id>default-cli</id>
          </execution>
          <execution>
            <id>debug</id>
            <configuration>
              <options>
                <option>-agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:8000</option>
              </options>
            </configuration>
          </execution>
          <execution>
            <id>ide-debug</id>
            <configuration>
              <options>
                <option>-agentlib:jdwp=transport=dt_socket,server=n,address=${jpda.address}</option>
              </options>
            </configuration>
          </execution>
          <execution>
            <id>ide-profile</id>
            <configuration>
              <options>
                <option>${profiler.jvmargs.arg1}</option>
                <option>${profiler.jvmargs.arg2}</option>
                <option>${profiler.jvmargs.arg3}</option>
                <option>${profiler.jvmargs.arg4}</option>
                <option>${profiler.jvmargs.arg5}</option>
              </options>
            </configuration>
          </execution>
          <execution>
            <id>jlink</id>
            <phase>package</phase>
            <goals>
              <goal>jlink</goal>
            </goals>
            <configuration>
              <launcher>fplayer</launcher>
              <mainClass>com.dev.fplayer/com.dev.fplayer.App</mainClass>
              <compress>2</compress>
              <stripDebug>true</stripDebug>
              <noHeaderFiles>true</noHeaderFiles>
              <noManPages>true</noManPages>
              <jlinkImageName>fplayer</jlinkImageName>
              <jlinkZipName>fplayer</jlinkZipName>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <mainClass>com.dev.fplayer.App</mainClass>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.panteleyev</groupId>
        <artifactId>jpackage-maven-plugin</artifactId>
        <version>1.4.0</version>
        <configuration>
          <name>FPlayer</name>
          <appVersion>1.0.0</appVersion>
          <vendor>FPlayer Team</vendor>
          <destination>target/installer</destination>
          <module>com.dev.fplayer/com.dev.fplayer.App</module>
          <runtimeImage>target/fplayer</runtimeImage>
          <javaOptions>
            <option>-Dfile.encoding=UTF-8</option>
          </javaOptions>
          <icon>src/main/resources/com/dev/fplayer/assets/app-icon.${icon.extension}</icon>
          <licenseFile>installer/LICENSE.txt</licenseFile>
          <aboutUrl>https://github.com/yourusername/fplayer</aboutUrl>
          <splashImage>src/main/resources/com/dev/fplayer/assets/splash.png</splashImage>
          <winMenu>true</winMenu>
          <winDirChooser>true</winDirChooser>
          <winShortcut>true</winShortcut>
          <winPerUserInstall>false</winPerUserInstall>
          <winMenuGroup>FPlayer</winMenuGroup>
          <macPackageIdentifier>com.dev.fplayer</macPackageIdentifier>
          <macPackageName>FPlayer</macPackageName>
          <macStartOnInstall>true</macStartOnInstall>
          <linuxPackageName>fplayer</linuxPackageName>
          <linuxDebMaintainer><EMAIL></linuxDebMaintainer>
          <linuxMenuGroup>AudioVideo</linuxMenuGroup>
          <linuxShortcut>true</linuxShortcut>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <properties>
    <maven.compiler.target>11</maven.compiler.target>
    <maven.compiler.source>11</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
</project>
