<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product Id="*" Name="FPlayer" Language="1033" Version="1.0.0.0" Manufacturer="FPlayer Team" UpgradeCode="12345678-1234-1234-1234-123456789012">
        <Package InstallerVersion="200" Compressed="yes" InstallScope="perMachine" />

        <MajorUpgrade DowngradeErrorMessage="A newer version of FPlayer is already installed." />
        <MediaTemplate EmbedCab="yes" />

        <Feature Id="ProductFeature" Title="FPlayer" Level="1">
            <ComponentGroupRef Id="ProductComponents" />
            <ComponentGroupRef Id="ProductShortcuts" />
        </Feature>

        <Icon Id="AppIcon.ico" SourceFile="$(var.AppIcon)" />
        <Property Id="ARPPRODUCTICON" Value="AppIcon.ico" />

        <UIRef Id="WixUI_Minimal" />
        <WixVariable Id="WixUILicenseRtf" Value="$(var.LicenseRtf)" />
        <WixVariable Id="WixUIBannerBmp" Value="$(var.BannerBmp)" />
        <WixVariable Id="WixUIDialogBmp" Value="$(var.DialogBmp)" />
    </Product>

    <Fragment>
        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="ProgramFilesFolder">
                <Directory Id="INSTALLFOLDER" Name="FPlayer" />
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="FPlayer" />
            </Directory>
            <Directory Id="DesktopFolder" Name="Desktop" />
        </Directory>
    </Fragment>

    <Fragment>
        <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
            <Component Id="ProductComponent" Guid="*">
                <File Id="FPlayerExe" Name="FPlayer.exe" Source="$(var.FPlayerExe)" KeyPath="yes" />
                <!-- Add additional files here -->
            </Component>
        </ComponentGroup>

        <ComponentGroup Id="ProductShortcuts" Directory="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut" 
                          Name="FPlayer" 
                          Description="Comprehensive Media Player"
                          Target="[#FPlayerExe]"
                          WorkingDirectory="INSTALLFOLDER" />
                <RemoveFolder Id="CleanUpShortcut" Directory="ApplicationProgramsFolder" On="uninstall" />
                <RegistryValue Root="HKCU" Key="Software\FPlayer" Name="installed" Type="integer" Value="1" KeyPath="yes" />
            </Component>
            <Component Id="DesktopShortcut" Guid="*">
                <Shortcut Id="ApplicationDesktopShortcut" 
                          Name="FPlayer" 
                          Description="Comprehensive Media Player"
                          Target="[#FPlayerExe]"
                          WorkingDirectory="INSTALLFOLDER" />
                <RegistryValue Root="HKCU" Key="Software\FPlayer" Name="desktop_sc" Type="integer" Value="1" KeyPath="yes" />
            </Component>
        </ComponentGroup>
    </Fragment>
</Wix>
