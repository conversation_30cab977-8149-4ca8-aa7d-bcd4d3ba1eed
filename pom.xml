<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.dev</groupId>
    <artifactId>FPlayer</artifactId>
    <version>1.0-SNAPSHOT</version>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>13</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>13</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-media</artifactId>
            <version>13</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-graphics</artifactId>
            <version>13</version>
        </dependency>
        <dependency>
            <groupId>org.controlsfx</groupId>
            <artifactId>controlsfx</artifactId>
            <version>11.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.jfoenix</groupId>
            <artifactId>jfoenix</artifactId>
            <version>9.0.10</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <release>11</release>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>com.dev.fplayer.App</mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>0.0.4</version>
                <configuration>
                    <mainClass>com.dev.fplayer.App</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <!-- Default configuration for running -->
                        <!-- Usage: mvn clean javafx:run -->
                        <id>default-cli</id>
                    </execution>
                    <execution>
                        <!-- Configuration for manual attach debugging -->
                        <!-- Usage: mvn clean javafx:run@debug -->
                        <id>debug</id>
                        <configuration>
                            <options>
                                <option>-agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=localhost:8000</option>
                            </options>
                        </configuration>
                    </execution>
                    <execution>
                        <!-- Configuration for automatic IDE debugging -->
                        <id>ide-debug</id>
                        <configuration>
                            <options>
                                <option>-agentlib:jdwp=transport=dt_socket,server=n,address=${jpda.address}</option>
                            </options>
                        </configuration>
                    </execution>
                    <execution>
                        <!-- Configuration for automatic IDE profiling -->
                        <id>ide-profile</id>
                        <configuration>
                            <options>
				<option>${profiler.jvmargs.arg1}</option>
				<option>${profiler.jvmargs.arg2}</option>
				<option>${profiler.jvmargs.arg3}</option>
				<option>${profiler.jvmargs.arg4}</option>
				<option>${profiler.jvmargs.arg5}</option>
                            </options>
                        </configuration>
                    </execution>
                    <execution>
                        <!-- Configuration for creating a modular runtime image -->
                        <!-- Usage: mvn clean javafx:jlink -->
                        <id>jlink</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jlink</goal>
                        </goals>
                        <configuration>
                            <launcher>fplayer</launcher>
                            <mainClass>com.dev.fplayer/com.dev.fplayer.App</mainClass>
                            <compress>2</compress>
                            <stripDebug>true</stripDebug>
                            <noHeaderFiles>true</noHeaderFiles>
                            <noManPages>true</noManPages>
                            <jlinkImageName>fplayer</jlinkImageName>
                            <jlinkZipName>fplayer</jlinkZipName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.panteleyev</groupId>
                <artifactId>jpackage-maven-plugin</artifactId>
                <version>1.4.0</version>
                <configuration>
                    <name>FPlayer</name>
                    <appVersion>1.0.0</appVersion>
                    <vendor>FPlayer Team</vendor>
                    <destination>target/installer</destination>
                    <module>com.dev.fplayer/com.dev.fplayer.App</module>
                    <runtimeImage>target/fplayer</runtimeImage>
                    <javaOptions>
                        <option>-Dfile.encoding=UTF-8</option>
                    </javaOptions>
                    <icon>src/main/resources/com/dev/fplayer/assets/app-icon.${icon.extension}</icon>
                    <licenseFile>installer/LICENSE.txt</licenseFile>
                    <aboutUrl>https://github.com/yourusername/fplayer</aboutUrl>
                    <splashImage>src/main/resources/com/dev/fplayer/assets/splash.png</splashImage>
                    <winMenu>true</winMenu>
                    <winDirChooser>true</winDirChooser>
                    <winShortcut>true</winShortcut>
                    <winPerUserInstall>false</winPerUserInstall>
                    <winMenuGroup>FPlayer</winMenuGroup>
                    <macPackageIdentifier>com.dev.fplayer</macPackageIdentifier>
                    <macPackageName>FPlayer</macPackageName>
                    <macStartOnInstall>true</macStartOnInstall>
                    <linuxPackageName>fplayer</linuxPackageName>
                    <linuxDebMaintainer><EMAIL></linuxDebMaintainer>
                    <linuxMenuGroup>AudioVideo</linuxMenuGroup>
                    <linuxShortcut>true</linuxShortcut>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
