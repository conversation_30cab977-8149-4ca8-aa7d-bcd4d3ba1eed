module com.dev.fplayer {
    requires javafx.controls;
    requires javafx.fxml;
    requires transitive javafx.media;
    requires transitive javafx.graphics;
    requires transitive javafx.base;
    requires org.controlsfx.controls;
    requires com.jfoenix;

    opens com.dev.fplayer to javafx.fxml;
    opens com.dev.fplayer.controllers to javafx.fxml;
    opens com.dev.fplayer.components to javafx.fxml;
    opens com.dev.fplayer.models to javafx.fxml;
    opens com.dev.fplayer.utils to javafx.fxml;

    exports com.dev.fplayer;
    exports com.dev.fplayer.controllers;
    exports com.dev.fplayer.components;
    exports com.dev.fplayer.models;
    exports com.dev.fplayer.utils;
}
