package com.dev.fplayer.components;

import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.geometry.Insets;
import javafx.geometry.Orientation;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.control.Slider;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.media.AudioEqualizer;
import javafx.scene.media.EqualizerBand;
import javafx.scene.media.MediaPlayer;

import java.util.ArrayList;
import java.util.List;

/**
 * Component for audio equalization
 */
public class EqualizerComponent extends VBox {
    
    private MediaPlayer mediaPlayer;
    private AudioEqualizer equalizer;
    private List<DoubleProperty> bandProperties;
    private List<Slider> sliders;
    
    // Standard frequencies for a 10-band equalizer (in Hz)
    private static final double[] FREQUENCIES = {
        32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000
    };
    
    /**
     * Create a new equalizer component
     */
    public EqualizerComponent() {
        setSpacing(10);
        setPadding(new Insets(10));
        setAlignment(Pos.CENTER);
        
        Label titleLabel = new Label("Equalizer");
        titleLabel.getStyleClass().add("equalizer-title");
        
        HBox slidersBox = new HBox(10);
        slidersBox.setAlignment(Pos.CENTER);
        
        bandProperties = new ArrayList<>();
        sliders = new ArrayList<>();
        
        // Create sliders for each frequency band
        for (int i = 0; i < FREQUENCIES.length; i++) {
            DoubleProperty bandProperty = new SimpleDoubleProperty(0);
            bandProperties.add(bandProperty);
            
            VBox bandBox = new VBox(5);
            bandBox.setAlignment(Pos.CENTER);
            
            Slider slider = new Slider(-12, 12, 0);
            slider.setOrientation(Orientation.VERTICAL);
            slider.setPrefHeight(150);
            slider.valueProperty().bindBidirectional(bandProperty);
            sliders.add(slider);
            
            Label freqLabel = new Label(formatFrequency(FREQUENCIES[i]));
            freqLabel.getStyleClass().add("freq-label");
            
            Label valueLabel = new Label("0 dB");
            valueLabel.getStyleClass().add("value-label");
            
            // Update value label when slider changes
            slider.valueProperty().addListener((obs, oldVal, newVal) -> {
                valueLabel.setText(String.format("%.1f dB", newVal.doubleValue()));
            });
            
            bandBox.getChildren().addAll(valueLabel, slider, freqLabel);
            slidersBox.getChildren().add(bandBox);
        }
        
        // Preset buttons
        HBox presetBox = new HBox(10);
        presetBox.setAlignment(Pos.CENTER);
        
        presetBox.getChildren().addAll(
            createPresetButton("Flat", new double[]{0, 0, 0, 0, 0, 0, 0, 0, 0, 0}),
            createPresetButton("Bass Boost", new double[]{7, 6, 5, 3, 1, 0, 0, 0, 0, 0}),
            createPresetButton("Treble Boost", new double[]{0, 0, 0, 0, 0, 1, 3, 5, 6, 7}),
            createPresetButton("Vocal Boost", new double[]{-3, -2, -1, 0, 3, 4, 3, 0, -1, -2}),
            createPresetButton("Rock", new double[]{4, 3, 2, 0, -1, 0, 2, 3, 4, 3})
        );
        
        getChildren().addAll(titleLabel, slidersBox, presetBox);
    }
    
    /**
     * Create a preset button
     * @param name The name of the preset
     * @param values The values for each band
     * @return The preset button
     */
    private javafx.scene.control.Button createPresetButton(String name, double[] values) {
        javafx.scene.control.Button button = new javafx.scene.control.Button(name);
        button.getStyleClass().add("preset-button");
        
        button.setOnAction(e -> {
            for (int i = 0; i < values.length && i < bandProperties.size(); i++) {
                bandProperties.get(i).set(values[i]);
            }
        });
        
        return button;
    }
    
    /**
     * Format a frequency value for display
     * @param freq The frequency in Hz
     * @return The formatted frequency string
     */
    private String formatFrequency(double freq) {
        if (freq >= 1000) {
            return String.format("%.1fk", freq / 1000);
        } else {
            return String.format("%.0f", freq);
        }
    }
    
    /**
     * Connect the equalizer to a media player
     * @param mediaPlayer The media player to connect to
     */
    public void connectToMediaPlayer(MediaPlayer mediaPlayer) {
        this.mediaPlayer = mediaPlayer;
        
        if (mediaPlayer != null) {
            equalizer = mediaPlayer.getAudioEqualizer();
            equalizer.setEnabled(true);
            
            // Clear existing bands
            equalizer.getBands().clear();
            
            // Create bands for each frequency
            for (int i = 0; i < FREQUENCIES.length; i++) {
                EqualizerBand band = new EqualizerBand(FREQUENCIES[i], 1.0, 0);
                equalizer.getBands().add(band);
                
                // Bind the band gain to the slider value
                final int index = i;
                bandProperties.get(i).addListener((obs, oldVal, newVal) -> {
                    equalizer.getBands().get(index).setGain(newVal.doubleValue());
                });
                
                // Set initial value
                band.setGain(bandProperties.get(i).get());
            }
        }
    }
    
    /**
     * Enable or disable the equalizer
     * @param enabled True to enable, false to disable
     */
    public void setEnabled(boolean enabled) {
        if (equalizer != null) {
            equalizer.setEnabled(enabled);
        }
        
        // Enable/disable sliders
        for (Slider slider : sliders) {
            slider.setDisable(!enabled);
        }
    }
    
    /**
     * Check if the equalizer is enabled
     * @return True if enabled, false otherwise
     */
    public boolean isEnabled() {
        return equalizer != null && equalizer.isEnabled();
    }
    
    /**
     * Reset all bands to 0 dB
     */
    public void reset() {
        for (DoubleProperty property : bandProperties) {
            property.set(0);
        }
    }
}
