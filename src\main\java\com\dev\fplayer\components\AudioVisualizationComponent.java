package com.dev.fplayer.components;

import javafx.animation.AnimationTimer;
import javafx.animation.KeyFrame;
import javafx.animation.KeyValue;
import javafx.animation.Timeline;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.Slider;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.layout.VBox;
import javafx.scene.media.AudioSpectrumListener;
import javafx.scene.media.MediaPlayer;
import javafx.scene.paint.Color;
import javafx.scene.paint.CycleMethod;
import javafx.scene.paint.LinearGradient;
import javafx.scene.paint.Stop;
import javafx.util.Duration;

import java.util.Random;

/**
 * Enhanced component for audio visualization during playback
 */
public class AudioVisualizationComponent extends VBox {

    private Canvas canvas;
    private GraphicsContext gc;
    private MediaPlayer mediaPlayer;
    private ObjectProperty<VisualizationType> visualizationTypeProperty = new SimpleObjectProperty<>(
            VisualizationType.SPECTRUM);
    private Color primaryColor = Color.rgb(29, 185, 84); // Spotify green
    private Color secondaryColor = Color.rgb(29, 185, 84, 0.5);
    private Color backgroundColor = Color.rgb(30, 30, 30);

    // Spectrum data
    private float[] spectrumData;
    private int bands = 128;
    private double minDecibels = -60.0;
    private double maxDecibels = 0.0;

    // Animation
    private AnimationTimer animationTimer;
    private boolean isActive = false;

    // Particle system for particle visualization
    private Particle[] particles;
    private Random random = new Random();

    // Controls
    private ComboBox<VisualizationType> visualizationTypeComboBox;
    private Slider sensitivitySlider;
    private Slider colorSlider;

    /**
     * Visualization types
     */
    public enum VisualizationType {
        SPECTRUM("Spectrum"),
        WAVEFORM("Waveform"),
        BARS("Bars"),
        CIRCLE("Circle"),
        PARTICLE("Particles"),
        OSCILLOSCOPE("Oscilloscope"),
        FIRE("Fire"),
        GRADIENT_BARS("Gradient Bars");

        private final String displayName;

        VisualizationType(String displayName) {
            this.displayName = displayName;
        }

        @Override
        public String toString() {
            return displayName;
        }
    }

    /**
     * Particle class for particle visualization
     */
    private class Particle {
        double x, y;
        double vx, vy;
        double life;
        double size;
        Color color;

        void reset(double x, double y, double intensity) {
            this.x = x;
            this.y = y;
            this.vx = (random.nextDouble() - 0.5) * 2;
            this.vy = -random.nextDouble() * 3 - 1 - intensity * 2;
            this.life = random.nextDouble() * 0.5 + 0.5;
            this.size = random.nextDouble() * 5 + 2 + intensity * 3;

            // Generate color based on intensity and colorSlider value
            double hue = (colorSlider.getValue() + intensity * 60) % 360;
            this.color = Color.hsb(hue, 0.8, 0.9, life);
        }

        void update() {
            x += vx;
            y += vy;
            vy += 0.05; // gravity
            life -= 0.01;
            size *= 0.99;
        }

        boolean isDead() {
            return life <= 0 || size <= 0.5;
        }
    }

    /**
     * Create a new audio visualization component
     */
    public AudioVisualizationComponent() {
        setSpacing(10);
        setPadding(new Insets(10));
        setAlignment(Pos.CENTER);

        // Create canvas pane
        Pane canvasPane = new Pane();
        canvas = new Canvas();
        gc = canvas.getGraphicsContext2D();
        canvasPane.getChildren().add(canvas);

        // Bind canvas size to pane size
        canvas.widthProperty().bind(canvasPane.widthProperty());
        canvas.heightProperty().bind(canvasPane.heightProperty());

        // Set minimum size for the canvas pane
        canvasPane.setMinHeight(200);
        canvasPane.setPrefHeight(200);

        // Initialize spectrum data
        spectrumData = new float[bands];

        // Initialize particles
        particles = new Particle[200];
        for (int i = 0; i < particles.length; i++) {
            particles[i] = new Particle();
        }

        // Set up animation timer
        animationTimer = new AnimationTimer() {
            @Override
            public void handle(long now) {
                draw();
            }
        };

        // Redraw when size changes
        canvasPane.widthProperty().addListener((obs, oldVal, newVal) -> {
            if (isActive) {
                draw();
            }
        });

        canvasPane.heightProperty().addListener((obs, oldVal, newVal) -> {
            if (isActive) {
                draw();
            }
        });

        // Create controls
        HBox controlsBox = new HBox(15);
        controlsBox.setAlignment(Pos.CENTER);

        // Visualization type selector
        Label typeLabel = new Label("Visualization:");
        visualizationTypeComboBox = new ComboBox<>();
        visualizationTypeComboBox.getItems().addAll(VisualizationType.values());
        visualizationTypeComboBox.setValue(VisualizationType.SPECTRUM);

        // Bind the visualization type property to the combo box
        visualizationTypeProperty.bind(visualizationTypeComboBox.valueProperty());

        // Sensitivity slider
        Label sensitivityLabel = new Label("Sensitivity:");
        sensitivitySlider = new Slider(0.5, 2.0, 1.0);
        sensitivitySlider.setPrefWidth(100);

        // Color slider
        Label colorLabel = new Label("Color:");
        colorSlider = new Slider(0, 360, 120);
        colorSlider.setPrefWidth(100);

        // Update colors when slider changes
        colorSlider.valueProperty().addListener((obs, oldVal, newVal) -> {
            double hue = newVal.doubleValue();
            primaryColor = Color.hsb(hue, 0.8, 0.9);
            secondaryColor = Color.hsb(hue, 0.8, 0.5, 0.5);
        });

        // Add controls to the box
        controlsBox.getChildren().addAll(
                typeLabel, visualizationTypeComboBox,
                sensitivityLabel, sensitivitySlider,
                colorLabel, colorSlider);

        // Add components to the main layout
        getChildren().addAll(canvasPane, controlsBox);
    }

    /**
     * Connect to a media player
     *
     * @param mediaPlayer The media player to connect to
     */
    public void connectToMediaPlayer(MediaPlayer mediaPlayer) {
        // Disconnect from previous media player if any
        if (this.mediaPlayer != null) {
            this.mediaPlayer.setAudioSpectrumListener(null);
        }

        this.mediaPlayer = mediaPlayer;

        if (mediaPlayer != null) {
            // Set up audio spectrum listener
            mediaPlayer.setAudioSpectrumNumBands(bands);
            mediaPlayer.setAudioSpectrumInterval(0.033); // ~30 fps
            mediaPlayer.setAudioSpectrumThreshold((int) minDecibels);

            mediaPlayer.setAudioSpectrumListener(new AudioSpectrumListener() {
                @Override
                public void spectrumDataUpdate(double timestamp, double duration, float[] magnitudes, float[] phases) {
                    // Copy spectrum data
                    System.arraycopy(magnitudes, 0, spectrumData, 0, Math.min(magnitudes.length, spectrumData.length));
                }
            });
        }
    }

    /**
     * Start the visualization
     */
    public void start() {
        if (!isActive) {
            isActive = true;
            animationTimer.start();
        }
    }

    /**
     * Stop the visualization
     */
    public void stop() {
        if (isActive) {
            isActive = false;
            animationTimer.stop();

            // Clear the canvas
            gc.setFill(backgroundColor);
            gc.fillRect(0, 0, canvas.getWidth(), canvas.getHeight());
        }
    }

    /**
     * Draw the visualization
     */
    private void draw() {
        double width = canvas.getWidth();
        double height = canvas.getHeight();

        // Clear the canvas
        gc.setFill(backgroundColor);
        gc.fillRect(0, 0, width, height);

        if (mediaPlayer == null || spectrumData == null) {
            return;
        }

        // Apply sensitivity
        float[] adjustedData = new float[spectrumData.length];
        double sensitivity = sensitivitySlider.getValue();
        for (int i = 0; i < spectrumData.length; i++) {
            adjustedData[i] = (float) (spectrumData[i] * sensitivity);
        }

        switch (visualizationTypeProperty.get()) {
            case SPECTRUM:
                drawSpectrum(width, height, adjustedData);
                break;
            case WAVEFORM:
                drawWaveform(width, height, adjustedData);
                break;
            case BARS:
                drawBars(width, height, adjustedData);
                break;
            case CIRCLE:
                drawCircle(width, height, adjustedData);
                break;
            case PARTICLE:
                drawParticles(width, height, adjustedData);
                break;
            case OSCILLOSCOPE:
                drawOscilloscope(width, height, adjustedData);
                break;
            case FIRE:
                drawFire(width, height, adjustedData);
                break;
            case GRADIENT_BARS:
                drawGradientBars(width, height, adjustedData);
                break;
        }
    }

    /**
     * Draw spectrum visualization
     *
     * @param width  Canvas width
     * @param height Canvas height
     * @param data   Spectrum data
     */
    private void drawSpectrum(double width, double height, float[] data) {
        int usableBands = Math.min(bands, (int) width / 2);
        double barWidth = width / usableBands;

        gc.setFill(primaryColor);

        for (int i = 0; i < usableBands; i++) {
            // Convert decibels to a height value
            double decibels = data[i] - minDecibels;
            double barHeight = decibels * height / (maxDecibels - minDecibels);

            // Draw the bar
            gc.fillRect(i * barWidth, height - barHeight, barWidth - 1, barHeight);
        }
    }

    /**
     * Draw waveform visualization
     *
     * @param width  Canvas width
     * @param height Canvas height
     * @param data   Spectrum data
     */
    private void drawWaveform(double width, double height, float[] data) {
        int usableBands = Math.min(bands, (int) width / 2);
        double barWidth = width / usableBands;
        double centerY = height / 2;

        gc.setStroke(primaryColor);
        gc.setLineWidth(2);

        gc.beginPath();

        for (int i = 0; i < usableBands; i++) {
            // Convert decibels to a height value
            double decibels = data[i] - minDecibels;
            double amplitude = decibels * height / (maxDecibels - minDecibels) / 2;

            double x = i * barWidth;
            double y = centerY - amplitude;

            if (i == 0) {
                gc.moveTo(x, y);
            } else {
                gc.lineTo(x, y);
            }
        }

        // Mirror the waveform
        for (int i = usableBands - 1; i >= 0; i--) {
            double decibels = data[i] - minDecibels;
            double amplitude = decibels * height / (maxDecibels - minDecibels) / 2;

            double x = i * barWidth;
            double y = centerY + amplitude;

            gc.lineTo(x, y);
        }

        gc.closePath();
        gc.setFill(secondaryColor);
        gc.fill();
        gc.stroke();
    }

    /**
     * Draw bars visualization
     *
     * @param width  Canvas width
     * @param height Canvas height
     * @param data   Spectrum data
     */
    private void drawBars(double width, double height, float[] data) {
        int usableBands = Math.min(bands, (int) width / 4);
        double barWidth = width / usableBands;

        for (int i = 0; i < usableBands; i++) {
            // Convert decibels to a height value
            double decibels = data[i] - minDecibels;
            double barHeight = decibels * height / (maxDecibels - minDecibels);

            // Calculate color based on height
            double hue = (360.0 * i / usableBands) % 360;
            Color barColor = Color.hsb(hue, 0.8, 0.9);

            // Draw the bar
            gc.setFill(barColor);
            gc.fillRect(i * barWidth, height - barHeight, barWidth - 2, barHeight);
        }
    }

    /**
     * Draw circle visualization
     *
     * @param width  Canvas width
     * @param height Canvas height
     * @param data   Spectrum data
     */
    private void drawCircle(double width, double height, float[] data) {
        double centerX = width / 2;
        double centerY = height / 2;
        double minRadius = Math.min(width, height) / 10;
        double maxRadius = Math.min(width, height) / 2;

        int usableBands = Math.min(bands, 360);

        for (int i = 0; i < usableBands; i++) {
            // Convert decibels to a radius value
            double decibels = data[i] - minDecibels;
            double radiusOffset = decibels * (maxRadius - minRadius) / (maxDecibels - minDecibels);
            double radius = minRadius + radiusOffset;

            // Calculate angle
            double angle = Math.toRadians(i * 360.0 / usableBands);

            // Calculate point on circle
            double x = centerX + radius * Math.cos(angle);
            double y = centerY + radius * Math.sin(angle);

            // Draw line from center to point
            gc.setStroke(primaryColor);
            gc.setLineWidth(2);
            gc.strokeLine(centerX, centerY, x, y);

            // Draw point
            gc.setFill(secondaryColor);
            gc.fillOval(x - 3, y - 3, 6, 6);
        }
    }

    /**
     * Draw particles visualization
     *
     * @param width  Canvas width
     * @param height Canvas height
     * @param data   Spectrum data
     */
    private void drawParticles(double width, double height, float[] data) {
        int usableBands = Math.min(bands, (int) width / 8);
        double barWidth = width / usableBands;

        // Update existing particles
        for (Particle p : particles) {
            if (p.isDead()) {
                // Find a band with significant energy);
                int bandIndex = random.nextInt(usableBands);
                double decibels = data[bandIndex] - minDecibels;
                double intensity = decibels / (maxDecibels - minDecibels);
                // Only create particles for bands with significant energy
                if (intensity > 0.3) {
                    p.reset(bandIndex * barWidth + barWidth / 2, height, intensity);
                }
            } else {
                p.update();
            }
        }

        // Draw particles
        for (Particle p : particles) {
            if (!p.isDead()) {
                gc.setFill(p.color);
                gc.fillOval(p.x - p.size / 2, p.y - p.size / 2, p.size, p.size);
            }
        }
    }

    /**
     * Draw oscilloscope visualization
     *
     * @param width  Canvas width
     * @param height Canvas height
     * @param data   Spectrum data
     */
    private void drawOscilloscope(double width, double height, float[] data) {
        double centerY = height / 2;
        int usableBands = Math.min(bands, (int) width);
        double step = width / usableBands;

        gc.setStroke(primaryColor);
        gc.setLineWidth(2);

        gc.beginPath();

        // Draw the oscilloscope line
        for (int i = 0; i < usableBands; i++) {
            double x = i * step;
            double decibels = data[i] - minDecibels;
            double amplitude = decibels * height / (maxDecibels - minDecibels) / 2;
            double y = centerY + amplitude * Math.sin(i * 0.1 + System.currentTimeMillis() * 0.001);

            if (i == 0) {
                gc.moveTo(x, y);
            } else {
                gc.lineTo(x, y);
            }
        }

        gc.stroke();

        // Draw a glow effect
        gc.setStroke(secondaryColor);
        gc.setLineWidth(4);
        gc.stroke();
    }

    /**
     * Draw fire visualization
     *
     * @param width  Canvas width
     * @param height Canvas height
     * @param data   Spectrum data
     */
    private void drawFire(double width, double height, float[] data) {
        int usableBands = Math.min(bands, (int) width / 2);
        double barWidth = width / usableBands;

        for (int i = 0; i < usableBands; i++) {
            // Convert decibels to a height value
            double decibels = data[i] - minDecibels;
            double barHeight = decibels * height / (maxDecibels - minDecibels);

            // Create a gradient for the fire effect
            double x = i * barWidth;
            double y = height - barHeight;

            // Create fire gradient
            Stop[] stops = new Stop[] {
                    new Stop(0, Color.YELLOW),
                    new Stop(0.5, Color.RED),
                    new Stop(1, Color.BLACK)
            };

            LinearGradient gradient = new LinearGradient(
                    0, height - barHeight,
                    0, height,
                    false, CycleMethod.NO_CYCLE,
                    stops);

            gc.setFill(gradient);
            gc.fillRect(x, y, barWidth - 1, barHeight);

            // Add some random "sparks" at the top of each bar
            if (barHeight > height * 0.2 && random.nextDouble() < 0.2) {
                double sparkSize = random.nextDouble() * 3 + 2;
                gc.setFill(Color.WHITE);
                gc.fillOval(x + barWidth / 2 - sparkSize / 2, y - sparkSize / 2, sparkSize, sparkSize);
            }
        }
    }

    /**
     * Draw gradient bars visualization
     *
     * @param width  Canvas width
     * @param height Canvas height
     * @param data   Spectrum data
     */
    private void drawGradientBars(double width, double height, float[] data) {
        int usableBands = Math.min(bands, (int) width / 4);
        double barWidth = width / usableBands;

        // Base hue from the color slider
        double baseHue = colorSlider.getValue();

        for (int i = 0; i < usableBands; i++) {
            // Convert decibels to a height value
            double decibels = data[i] - minDecibels;
            double barHeight = decibels * height / (maxDecibels - minDecibels);

            // Calculate color based on position and height
            double hue = (baseHue + (360.0 * i / usableBands) * 0.5) % 360;
            double saturation = 0.8 + (barHeight / height) * 0.2;
            double brightness = 0.7 + (barHeight / height) * 0.3;

            // Create gradient
            Stop[] stops = new Stop[] {
                    new Stop(0, Color.hsb(hue, saturation, brightness)),
                    new Stop(1, Color.hsb(hue, saturation * 0.7, brightness * 0.5))
            };

            LinearGradient gradient = new LinearGradient(
                    0, height - barHeight,
                    0, height,
                    false, CycleMethod.NO_CYCLE,
                    stops);

            // Draw the bar with rounded top
            gc.setFill(gradient);

            // Draw rounded rectangle
            double x = i * barWidth;
            double y = height - barHeight;
            double arcSize = Math.min(barWidth - 2, 10);

            // Draw bar with rounded top
            gc.fillRoundRect(x, y, barWidth - 2, barHeight, arcSize, arcSize);

            // Add reflection effect
            gc.setFill(Color.WHITE.deriveColor(0, 0, 1, 0.1));
            gc.fillRoundRect(x + 2, y + 2, (barWidth - 2) / 2 - 2, barHeight * 0.9, arcSize / 2, arcSize / 2);
        }
    }

    /**
     * Set the visualization type
     *
     * @param type The visualization type
     */
    public void setVisualizationType(VisualizationType type) {
        this.visualizationTypeProperty.set(type);
        visualizationTypeComboBox.setValue(type);
    }

    /**
     * Get the current visualization type
     *
     * @return The visualization type
     */
    public VisualizationType getVisualizationType() {
        return visualizationTypeProperty.get();
    }

    /**
     * Get the visualization type property
     *
     * @return The visualization type property
     */
    public ObjectProperty<VisualizationType> visualizationTypeProperty() {
        return visualizationTypeProperty;
    }

    /**
     * Set the primary color
     *
     * @param color The primary color
     */
    public void setPrimaryColor(Color color) {
        this.primaryColor = color;
    }

    /**
     * Set the secondary color
     *
     * @param color The secondary color
     */
    public void setSecondaryColor(Color color) {
        this.secondaryColor = color;
    }

    /**
     * Set the background color
     *
     * @param color The background color
     */
    public void setBackgroundColor(Color color) {
        this.backgroundColor = color;
    }

    /**
     * Set the number of frequency bands
     *
     * @param bands The number of bands
     */
    public void setBands(int bands) {
        this.bands = bands;
        spectrumData = new float[bands];

        if (mediaPlayer != null) {
            mediaPlayer.setAudioSpectrumNumBands(bands);
        }
    }

    /**
     * Set the minimum decibel level
     *
     * @param minDecibels The minimum decibel level
     */
    public void setMinDecibels(double minDecibels) {
        this.minDecibels = minDecibels;

        if (mediaPlayer != null) {
            mediaPlayer.setAudioSpectrumThreshold((int) minDecibels);
        }
    }

    /**
     * Set the maximum decibel level
     *
     * @param maxDecibels The maximum decibel level
     */
    public void setMaxDecibels(double maxDecibels) {
        this.maxDecibels = maxDecibels;
    }
}
