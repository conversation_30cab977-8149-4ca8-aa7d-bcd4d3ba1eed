/* Dark Theme - Default */
.root {
    -fx-font-family: "Segoe UI", Arial, sans-serif;
    -fx-background-color: #2b2b2b;
    -fx-text-fill: white;
}

/* Menu bar styles */
.menu-bar {
    -fx-background-color: #1e1e1e;
}

.menu-bar .label {
    -fx-text-fill: white;
}

.menu-item {
    -fx-background-color: #1e1e1e;
}

.menu-item .label {
    -fx-text-fill: white;
}

.menu-item:hover {
    -fx-background-color: #3c3c3c;
}

/* Button styles */
.button {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: white;
    -fx-background-radius: 3;
}

.button:hover {
    -fx-background-color: #4c4c4c;
}

.button:pressed {
    -fx-background-color: #5c5c5c;
}

/* Transport control buttons */
#transportControls .button {
    -fx-font-size: 16px;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-background-radius: 20;
}

#playButton {
    -fx-background-color: #1db954;
}

#playButton:hover {
    -fx-background-color: #1ed760;
}

/* Slider styles */
.slider .track {
    -fx-background-color: #5c5c5c;
}

.slider .thumb {
    -fx-background-color: #1db954;
}

/* List view styles */
.list-view {
    -fx-background-color: #2b2b2b;
    -fx-control-inner-background: #2b2b2b;
}

.list-cell {
    -fx-background-color: #2b2b2b;
    -fx-text-fill: white;
}

.list-cell:filled:selected {
    -fx-background-color: #1db954;
}

.list-cell:filled:hover {
    -fx-background-color: #3c3c3c;
}

/* Tab pane styles */
.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: #1e1e1e;
}

.tab-pane .tab {
    -fx-background-color: #2b2b2b;
}

.tab-pane .tab:selected {
    -fx-background-color: #3c3c3c;
}

.tab .tab-label {
    -fx-text-fill: white;
}

/* Equalizer styles */
.equalizer-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.freq-label {
    -fx-text-fill: white;
    -fx-font-size: 10px;
}

.value-label {
    -fx-text-fill: white;
    -fx-font-size: 10px;
}

.preset-button {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: white;
    -fx-background-radius: 3;
    -fx-font-size: 11px;
}

.preset-button:hover {
    -fx-background-color: #4c4c4c;
}

/* Sound effects styles */
.effects-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.effect-button {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: white;
    -fx-background-radius: 3;
    -fx-font-size: 11px;
}

.effect-button:hover {
    -fx-background-color: #4c4c4c;
}

/* Split pane styles */
.split-pane {
    -fx-background-color: #2b2b2b;
}

.split-pane-divider {
    -fx-background-color: #1e1e1e;
    -fx-padding: 0 1 0 1;
}

/* Label styles */
.label {
    -fx-text-fill: white;
}

/* Check box styles */
.check-box {
    -fx-text-fill: white;
}

.check-box .box {
    -fx-background-color: #3c3c3c;
}

.check-box:selected .mark {
    -fx-background-color: white;
}

.check-box:selected .box {
    -fx-background-color: #1db954;
}

/* Dialog styles */
.dialog-pane {
    -fx-background-color: #2b2b2b;
}

.dialog-pane .label {
    -fx-text-fill: white;
}

.dialog-pane:header .header-panel {
    -fx-background-color: #1e1e1e;
}

.dialog-pane:header .header-panel .label {
    -fx-text-fill: white;
}

/* Text field styles */
.text-field {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: white;
    -fx-prompt-text-fill: #a0a0a0;
}

/* Combo box styles */
.combo-box {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: white;
}

.combo-box .list-cell {
    -fx-text-fill: white;
}

.combo-box-popup .list-view {
    -fx-background-color: #3c3c3c;
}

/* Scroll bar styles */
.scroll-bar {
    -fx-background-color: #2b2b2b;
}

.scroll-bar .thumb {
    -fx-background-color: #5c5c5c;
}

.scroll-bar .increment-button,
.scroll-bar .decrement-button {
    -fx-background-color: #3c3c3c;
}

/* Table view styles */
.table-view {
    -fx-background-color: #2b2b2b;
    -fx-table-cell-border-color: transparent;
}

.table-view .column-header-background {
    -fx-background-color: #1e1e1e;
}

.table-view .column-header, .table-view .filler {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.table-view .column-header .label {
    -fx-text-fill: white;
}

.table-row-cell {
    -fx-background-color: #2b2b2b;
    -fx-text-fill: white;
}

.table-row-cell:selected {
    -fx-background-color: #1db954;
}

/* Visualization styles */
.visualization-container {
    -fx-background-color: #1e1e1e;
    -fx-border-color: #3c3c3c;
    -fx-border-width: 1;
}

/* Tooltip styles */
.tooltip {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: white;
}

/* Progress bar styles */
.progress-bar {
    -fx-background-color: #3c3c3c;
}

.progress-bar .bar {
    -fx-background-color: #1db954;
}

/* Separator styles */
.separator .line {
    -fx-border-color: #3c3c3c;
    -fx-border-width: 1;
}
