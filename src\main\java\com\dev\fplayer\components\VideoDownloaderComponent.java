package com.dev.fplayer.components;

import com.dev.fplayer.models.MediaItem;
import javafx.application.Platform;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.DirectoryChooser;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Component for downloading videos in different qualities
 */
public class VideoDownloaderComponent extends BorderPane {
    
    private final ObservableList<DownloadTask> downloadTasks = FXCollections.observableArrayList();
    private final ExecutorService executorService = Executors.newFixedThreadPool(3);
    private final TableView<DownloadTask> downloadsTableView;
    private TextField urlTextField;
    private ComboBox<String> qualityComboBox;
    private File downloadDirectory;
    
    /**
     * Create a new video downloader component
     */
    public VideoDownloaderComponent() {
        setPadding(new Insets(10));
        
        // Create the top controls
        VBox topControls = new VBox(10);
        topControls.setPadding(new Insets(10));
        
        // URL input
        HBox urlBox = new HBox(10);
        urlBox.setAlignment(Pos.CENTER_LEFT);
        Label urlLabel = new Label("URL:");
        urlTextField = new TextField();
        urlTextField.setPromptText("Enter video URL");
        urlTextField.setPrefWidth(400);
        Button fetchButton = new Button("Fetch");
        fetchButton.setOnAction(e -> fetchVideoInfo());
        urlBox.getChildren().addAll(urlLabel, urlTextField, fetchButton);
        
        // Quality selection
        HBox qualityBox = new HBox(10);
        qualityBox.setAlignment(Pos.CENTER_LEFT);
        Label qualityLabel = new Label("Quality:");
        qualityComboBox = new ComboBox<>();
        qualityComboBox.getItems().addAll("1080p", "720p", "480p", "360p", "240p");
        qualityComboBox.setValue("720p");
        qualityComboBox.setPrefWidth(150);
        qualityBox.getChildren().addAll(qualityLabel, qualityComboBox);
        
        // Download location
        HBox locationBox = new HBox(10);
        locationBox.setAlignment(Pos.CENTER_LEFT);
        Label locationLabel = new Label("Save to:");
        TextField locationTextField = new TextField();
        locationTextField.setEditable(false);
        locationTextField.setPrefWidth(300);
        Button browseButton = new Button("Browse");
        browseButton.setOnAction(e -> {
            DirectoryChooser directoryChooser = new DirectoryChooser();
            directoryChooser.setTitle("Select Download Location");
            File directory = directoryChooser.showDialog(getScene().getWindow());
            if (directory != null) {
                downloadDirectory = directory;
                locationTextField.setText(directory.getAbsolutePath());
            }
        });
        locationBox.getChildren().addAll(locationLabel, locationTextField, browseButton);
        
        // Download button
        Button downloadButton = new Button("Download");
        downloadButton.setOnAction(e -> startDownload());
        
        topControls.getChildren().addAll(urlBox, qualityBox, locationBox, downloadButton);
        
        // Create the downloads table
        downloadsTableView = new TableView<>();
        downloadsTableView.setItems(downloadTasks);
        
        // File name column
        TableColumn<DownloadTask, String> fileNameColumn = new TableColumn<>("File Name");
        fileNameColumn.setCellValueFactory(cellData -> cellData.getValue().fileNameProperty());
        fileNameColumn.setPrefWidth(200);
        
        // Quality column
        TableColumn<DownloadTask, String> qualityColumn = new TableColumn<>("Quality");
        qualityColumn.setCellValueFactory(cellData -> cellData.getValue().qualityProperty());
        qualityColumn.setPrefWidth(100);
        
        // Progress column
        TableColumn<DownloadTask, Double> progressColumn = new TableColumn<>("Progress");
        progressColumn.setCellValueFactory(cellData -> cellData.getValue().progressProperty().asObject());
        progressColumn.setCellFactory(column -> new TableCell<DownloadTask, Double>() {
            private final ProgressBar progressBar = new ProgressBar();
            
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setGraphic(null);
                } else {
                    progressBar.setProgress(item);
                    setGraphic(progressBar);
                }
            }
        });
        progressColumn.setPrefWidth(200);
        
        // Status column
        TableColumn<DownloadTask, String> statusColumn = new TableColumn<>("Status");
        statusColumn.setCellValueFactory(cellData -> cellData.getValue().statusProperty());
        statusColumn.setPrefWidth(100);
        
        // Action column
        TableColumn<DownloadTask, Void> actionColumn = new TableColumn<>("Action");
        actionColumn.setCellFactory(column -> new TableCell<DownloadTask, Void>() {
            private final Button cancelButton = new Button("Cancel");
            
            {
                cancelButton.setOnAction(event -> {
                    DownloadTask task = getTableView().getItems().get(getIndex());
                    task.cancel();
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(cancelButton);
                }
            }
        });
        actionColumn.setPrefWidth(100);
        
        downloadsTableView.getColumns().addAll(fileNameColumn, qualityColumn, progressColumn, statusColumn, actionColumn);
        
        // Set the layout
        setTop(topControls);
        setCenter(downloadsTableView);
    }
    
    /**
     * Fetch video information from the URL
     */
    private void fetchVideoInfo() {
        String url = urlTextField.getText().trim();
        if (url.isEmpty()) {
            showAlert("Error", "Please enter a valid URL");
            return;
        }
        
        // In a real implementation, this would parse the video URL and extract available qualities
        // For this example, we'll just use the predefined qualities
    }
    
    /**
     * Start downloading the video
     */
    private void startDownload() {
        String url = urlTextField.getText().trim();
        String quality = qualityComboBox.getValue();
        
        if (url.isEmpty()) {
            showAlert("Error", "Please enter a valid URL");
            return;
        }
        
        if (downloadDirectory == null) {
            showAlert("Error", "Please select a download location");
            return;
        }
        
        // Create a file name from the URL
        String fileName = url.substring(url.lastIndexOf('/') + 1);
        if (fileName.isEmpty()) {
            fileName = "video_" + System.currentTimeMillis() + ".mp4";
        } else if (!fileName.endsWith(".mp4")) {
            fileName += ".mp4";
        }
        
        // Create the download task
        DownloadTask task = new DownloadTask(url, quality, new File(downloadDirectory, fileName));
        downloadTasks.add(task);
        
        // Start the download
        executorService.submit(task);
    }
    
    /**
     * Show an alert dialog
     * @param title The alert title
     * @param message The alert message
     */
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    /**
     * Dispose of resources when no longer needed
     */
    public void dispose() {
        executorService.shutdownNow();
    }
    
    /**
     * Class representing a download task
     */
    public class DownloadTask implements Runnable {
        private final String url;
        private final String quality;
        private final File outputFile;
        private final StringProperty fileName = new SimpleStringProperty();
        private final StringProperty qualityProperty = new SimpleStringProperty();
        private final DoubleProperty progress = new SimpleDoubleProperty(0);
        private final StringProperty status = new SimpleStringProperty("Pending");
        private volatile boolean cancelled = false;
        
        /**
         * Create a new download task
         * @param url The URL to download from
         * @param quality The video quality
         * @param outputFile The output file
         */
        public DownloadTask(String url, String quality, File outputFile) {
            this.url = url;
            this.quality = quality;
            this.outputFile = outputFile;
            this.fileName.set(outputFile.getName());
            this.qualityProperty.set(quality);
        }
        
        @Override
        public void run() {
            try {
                Platform.runLater(() -> status.set("Downloading"));
                
                // Open connection to the URL
                URL videoUrl = new URL(url);
                HttpURLConnection connection = (HttpURLConnection) videoUrl.openConnection();
                int fileSize = connection.getContentLength();
                
                // Create the output file
                try (InputStream in = connection.getInputStream();
                     FileOutputStream out = new FileOutputStream(outputFile)) {
                    
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    long totalBytesRead = 0;
                    
                    while ((bytesRead = in.read(buffer)) != -1) {
                        if (cancelled) {
                            Platform.runLater(() -> status.set("Cancelled"));
                            return;
                        }
                        
                        out.write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;
                        
                        // Update progress
                        final double currentProgress = (double) totalBytesRead / fileSize;
                        Platform.runLater(() -> progress.set(currentProgress));
                    }
                }
                
                Platform.runLater(() -> {
                    progress.set(1.0);
                    status.set("Completed");
                });
                
            } catch (IOException e) {
                Platform.runLater(() -> status.set("Failed"));
                e.printStackTrace();
            }
        }
        
        /**
         * Cancel the download
         */
        public void cancel() {
            cancelled = true;
        }
        
        /**
         * Get the file name property
         * @return The file name property
         */
        public StringProperty fileNameProperty() {
            return fileName;
        }
        
        /**
         * Get the quality property
         * @return The quality property
         */
        public StringProperty qualityProperty() {
            return qualityProperty;
        }
        
        /**
         * Get the progress property
         * @return The progress property
         */
        public DoubleProperty progressProperty() {
            return progress;
        }
        
        /**
         * Get the status property
         * @return The status property
         */
        public StringProperty statusProperty() {
            return status;
        }
    }
}
