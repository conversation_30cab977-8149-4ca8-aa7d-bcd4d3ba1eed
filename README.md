# FPlayer - Comprehensive Media Player

FPlayer is a feature-rich media player built with JavaFX that supports a wide range of audio and video formats.

## Features

- Audio and video playback
- Playlist management
- Media library organization
- Equalizer and sound effects
- Audio visualizations
- Subtitle support
- Streaming capabilities
- Video downloading
- Customizable themes
- Keyboard shortcuts

## Building from Source

### Prerequisites

- JDK 11 or later
- Maven 3.6 or later
- JavaFX 13 or later

### Build Steps

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/fplayer.git
   cd fplayer
   ```

2. Build the application:
   ```
   mvn clean package
   ```

3. Run the application:
   ```
   mvn javafx:run
   ```

### Creating Installers

#### Creating a Runtime Image

To create a custom runtime image with jlink:

```
mvn javafx:jlink
```

This will create a runtime image in the `target/fplayer` directory.

#### Creating Platform-Specific Installers

To create platform-specific installers:

1. First, create a runtime image:
   ```
   mvn javafx:jlink
   ```

2. Then, create the installer for your platform:

   **Windows:**
   ```
   cd installer/windows
   build-installer.bat
   ```

   **macOS:**
   ```
   cd installer/macos
   chmod +x build-installer.sh
   ./build-installer.sh
   ```

   **Linux:**
   ```
   cd installer/linux
   chmod +x build-installer.sh
   ./build-installer.sh
   ```

The installers will be created in the `target/installer` directory.

## License

This project is licensed under the MIT License - see the [LICENSE.txt](installer/LICENSE.txt) file for details.
