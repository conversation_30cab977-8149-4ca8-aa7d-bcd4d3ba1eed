# FPlayer - Comprehensive Media Player

FPlayer is a feature-rich media player built with JavaFX that supports a wide range of audio and video formats.

## Features

- Audio and video playback
- Playlist management
- Media library organization
- Equalizer and sound effects
- Audio visualizations
- Subtitle support
- Streaming capabilities
- Video downloading
- Customizable themes
- Keyboard shortcuts

## Building from Source

### Prerequisites

- JDK 11 or later
- Maven 3.6 or later
- JavaFX 13 or later

### Build Steps

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/fplayer.git
   cd fplayer
   ```

2. Build the application:
   ```
   mvn clean package
   ```

3. Run the application:
   ```
   mvn javafx:run
   ```

## Quick Start

### Windows
1. Double-click `FPlayer.bat` to run the application
2. Or use PowerShell: `.\FPlayer.ps1`

### Linux/macOS
1. Make the script executable: `chmod +x FPlayer.sh`
2. Run the application: `./FPlayer.sh`

## Usage

### Basic Controls
- **Play/Pause**: Spacebar or click the play button
- **Stop**: S key or click the stop button
- **Next/Previous**: Arrow keys or navigation buttons
- **Volume**: Up/Down arrows or volume slider
- **Mute**: M key or click the mute button
- **Fullscreen**: F key or click the fullscreen button

### Keyboard Shortcuts
- `Space`: Play/Pause
- `S`: Stop
- `←/→`: Previous/Next track
- `↑/↓`: Volume up/down
- `M`: Mute/Unmute
- `F`: Toggle fullscreen
- `Ctrl+O`: Open file
- `Ctrl+L`: Open folder
- `Ctrl+N`: New playlist

### File Formats Supported
- **Video**: MP4, AVI, MKV, MOV, WMV, FLV
- **Audio**: MP3, WAV, AAC, FLAC, OGG, M4A

## Responsive Design

FPlayer automatically adapts to different screen sizes:
- **Mobile (< 600px)**: Compact layout with smaller controls
- **Tablet (600px - 1024px)**: Balanced layout
- **Desktop (> 1024px)**: Full-featured layout with larger controls
- **Large Screens (> 1200px)**: Enhanced spacing and larger elements

### Creating Installers

#### Creating a Runtime Image

To create a custom runtime image with jlink:

```
mvn javafx:jlink
```

This will create a runtime image in the `target/fplayer` directory.

#### Creating Platform-Specific Installers

To create platform-specific installers:

1. First, create a runtime image:
   ```
   mvn javafx:jlink
   ```

2. Then, create the installer for your platform:

   **Windows:**
   ```
   cd installer/windows
   build-installer.bat
   ```

   **macOS:**
   ```
   cd installer/macos
   chmod +x build-installer.sh
   ./build-installer.sh
   ```

   **Linux:**
   ```
   cd installer/linux
   chmod +x build-installer.sh
   ./build-installer.sh
   ```

The installers will be created in the `target/installer` directory.

## License

This project is licensed under the MIT License - see the [LICENSE.txt](installer/LICENSE.txt) file for details.
