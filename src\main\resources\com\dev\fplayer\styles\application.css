/* Main application styling with responsive design */
.root {
    -fx-background-color: #1e1e1e;
    -fx-font-family: 'Segoe UI', Arial, sans-serif;
    -fx-font-size: 12px;
    -fx-text-fill: white;
    -fx-focus-color: #1DB954;
    -fx-faint-focus-color: #1DB95422;
}

/* Responsive font sizes based on screen size */
@media screen and (max-width: 800px) {
    .root {
        -fx-font-size: 10px;
    }
}

@media screen and (min-width: 1200px) {
    .root {
        -fx-font-size: 14px;
    }
}

@media screen and (min-width: 1600px) {
    .root {
        -fx-font-size: 16px;
    }
}

/* Main pane styling for better layout */
.main-pane {
    -fx-background-color: #1e1e1e;
    -fx-padding: 0;
    -fx-border-width: 0;
}

/* Split pane styling for better component fitting */
.split-pane {
    -fx-background-color: #1e1e1e;
    -fx-padding: 0;
    -fx-background-insets: 0;
    -fx-border-width: 0;
}

.split-pane:horizontal > .split-pane-divider {
    -fx-background-color: #333333;
    -fx-padding: 0 1px 0 1px;
}

.split-pane:vertical > .split-pane-divider {
    -fx-background-color: #333333;
    -fx-padding: 1px 0 1px 0;
}

/* Responsive media player component styling */
.media-player-component {
    -fx-background-color: #121212;
    -fx-border-color: #333333;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-fit-to-width: true;
    -fx-fit-to-height: true;
    -fx-alignment: center;
    -fx-min-width: 200px;
    -fx-min-height: 112px; /* 16:9 ratio */
}

.media-container {
    -fx-background-color: #121212;
    -fx-border-color: #333333;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-alignment: center;
    -fx-min-width: 200px;
    -fx-min-height: 112px;
}

/* Responsive media container for different screen sizes */
@media screen and (max-width: 600px) {
    .media-container {
        -fx-min-width: 280px;
        -fx-min-height: 157px;
        -fx-pref-width: 320px;
        -fx-pref-height: 180px;
    }
}

@media screen and (min-width: 600px) and (max-width: 1024px) {
    .media-container {
        -fx-min-width: 400px;
        -fx-min-height: 225px;
        -fx-pref-width: 640px;
        -fx-pref-height: 360px;
    }
}

@media screen and (min-width: 1024px) {
    .media-container {
        -fx-min-width: 640px;
        -fx-min-height: 360px;
        -fx-pref-width: 960px;
        -fx-pref-height: 540px;
    }
}

.media-view {
    -fx-background-color: black;
    -fx-preserve-ratio: true;
    -fx-smooth: true;
}

.media-status-label {
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-background-color: rgba(0, 0, 0, 0.7);
    -fx-padding: 10px;
    -fx-background-radius: 5px;
    -fx-alignment: center;
    -fx-text-alignment: center;
    -fx-wrap-text: true;
    -fx-max-width: 80%;
}

.loading-indicator {
    -fx-progress-color: #1DB954;
    -fx-max-width: 120px;
    -fx-max-height: 120px;
    -fx-min-width: 80px;
    -fx-min-height: 80px;
}

/* Responsive control buttons styling */
.button {
    -fx-background-color: #333333;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 6px 12px;
    -fx-cursor: hand;
    -fx-min-width: 32px;
    -fx-min-height: 32px;
    -fx-content-display: graphic-only;
    -fx-font-size: 1em; /* Relative to root font size */
}

.button:hover {
    -fx-background-color: #444444;
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.6), 10, 0, 0, 0);
}

.button:pressed {
    -fx-background-color: #555555;
    -fx-effect: innershadow(gaussian, rgba(0, 0, 0, 0.6), 10, 0, 0, 0);
}

/* Responsive button sizes for different screen sizes */
@media screen and (max-width: 600px) {
    .button {
        -fx-min-width: 28px;
        -fx-min-height: 28px;
        -fx-padding: 4px 8px;
    }
}

@media screen and (min-width: 1200px) {
    .button {
        -fx-min-width: 36px;
        -fx-min-height: 36px;
        -fx-padding: 8px 16px;
    }
}

/* Responsive transport controls styling */
.transport-controls {
    -fx-background-color: #252525;
    -fx-padding: 5px;
    -fx-background-radius: 5px;
    -fx-border-color: #333333;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-alignment: center;
    -fx-spacing: 5px;
}

.transport-controls .button {
    -fx-background-color: transparent;
    -fx-padding: 3px;
    -fx-min-width: 28px;
    -fx-min-height: 28px;
    -fx-max-width: 28px;
    -fx-max-height: 28px;
}

.transport-controls .button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive play button styling */
#playButton {
    -fx-background-color: #1DB954;
    -fx-background-radius: 50%;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-max-width: 40px;
    -fx-max-height: 40px;
    -fx-padding: 0;
    -fx-content-display: graphic-only;
}

/* Responsive transport controls for different screen sizes */
@media screen and (max-width: 600px) {
    .transport-controls {
        -fx-padding: 3px;
        -fx-spacing: 3px;
    }

    .transport-controls .button {
        -fx-min-width: 24px;
        -fx-min-height: 24px;
        -fx-max-width: 24px;
        -fx-max-height: 24px;
    }

    #playButton {
        -fx-min-width: 32px;
        -fx-min-height: 32px;
        -fx-max-width: 32px;
        -fx-max-height: 32px;
    }
}

@media screen and (min-width: 1200px) {
    .transport-controls {
        -fx-padding: 8px;
        -fx-spacing: 8px;
    }

    .transport-controls .button {
        -fx-min-width: 32px;
        -fx-min-height: 32px;
        -fx-max-width: 32px;
        -fx-max-height: 32px;
    }

    #playButton {
        -fx-min-width: 48px;
        -fx-min-height: 48px;
        -fx-max-width: 48px;
        -fx-max-height: 48px;
    }
}

#playButton:hover {
    -fx-background-color: #1ed760;
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.8), 15, 0, 0, 0);
}

#playButton:pressed {
    -fx-background-color: #1aa34a;
}

/* Enhanced slider styling */
.slider {
    -fx-control-inner-background: #333333;
    -fx-accent: #1DB954;
    -fx-show-tick-marks: false;
    -fx-show-tick-labels: false;
    -fx-snap-to-ticks: false;
}

.slider .thumb {
    -fx-background-color: linear-gradient(to bottom, #1ed760, #1DB954);
    -fx-background-radius: 50%;
    -fx-pref-height: 18px;
    -fx-pref-width: 18px;
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.6), 8, 0, 0, 0);
    -fx-border-color: rgba(255, 255, 255, 0.2);
    -fx-border-width: 1px;
    -fx-border-radius: 50%;
}

.slider .thumb:hover {
    -fx-background-color: linear-gradient(to bottom, #22e068, #1ed760);
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.8), 12, 0, 0, 0);
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

.slider .track {
    -fx-background-color: linear-gradient(to right, #333333, #2a2a2a);
    -fx-background-radius: 3px;
    -fx-pref-height: 6px;
    -fx-effect: innershadow(gaussian, rgba(0, 0, 0, 0.3), 3, 0, 0, 0);
}

/* Enhanced time slider styling */
.time-slider {
    -fx-pref-height: 35px;
    -fx-cursor: hand;
}

.time-slider .thumb {
    -fx-background-color: linear-gradient(to bottom, #1ed760, #1DB954);
    -fx-background-radius: 50%;
    -fx-pref-height: 20px;
    -fx-pref-width: 20px;
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.7), 10, 0, 0, 0);
    -fx-border-color: rgba(255, 255, 255, 0.3);
    -fx-border-width: 2px;
    -fx-border-radius: 50%;
}

.time-slider .thumb:hover {
    -fx-background-color: linear-gradient(to bottom, #22e068, #1ed760);
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.9), 15, 0, 0, 0);
    -fx-scale-x: 1.2;
    -fx-scale-y: 1.2;
}

.time-slider .track {
    -fx-background-color: linear-gradient(to right, #333333, #2a2a2a);
    -fx-background-radius: 4px;
    -fx-pref-height: 8px;
    -fx-effect: innershadow(gaussian, rgba(0, 0, 0, 0.4), 4, 0, 0, 0);
}

/* Volume slider specific styling */
.volume-slider {
    -fx-pref-width: 120px;
}

.volume-slider .thumb {
    -fx-background-color: #1DB954;
    -fx-background-radius: 50%;
    -fx-pref-height: 14px;
    -fx-pref-width: 14px;
}

.volume-slider .track {
    -fx-background-color: #333333;
    -fx-background-radius: 2px;
    -fx-pref-height: 4px;
}

/* Responsive tab pane styling */
.tab-pane {
    -fx-background-color: #1e1e1e;
    -fx-tab-min-width: 80px;
    -fx-tab-max-width: 200px;
    -fx-tab-min-height: 30px;
    -fx-padding: 0;
}

/* Responsive tab sizing for different screen sizes */
@media screen and (max-width: 600px) {
    .tab-pane {
        -fx-tab-min-width: 60px;
        -fx-tab-max-width: 120px;
        -fx-tab-min-height: 25px;
    }
}

@media screen and (min-width: 1200px) {
    .tab-pane {
        -fx-tab-min-width: 120px;
        -fx-tab-max-width: 250px;
        -fx-tab-min-height: 35px;
    }
}

.tab {
    -fx-background-color: #333333;
    -fx-text-fill: white;
    -fx-background-radius: 4px 4px 0 0;
    -fx-padding: 5px 10px;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
}

.tab:selected {
    -fx-background-color: #1DB954;
    -fx-text-fill: white;
    -fx-font-weight: bold;
}

.tab:hover {
    -fx-background-color: #444444;
}

.tab:selected:hover {
    -fx-background-color: #1ed760;
}

.tab-pane .tab-header-area {
    -fx-padding: 5px 0 0 0;
}

.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: #1e1e1e;
    -fx-border-color: #333333;
    -fx-border-width: 0 0 1px 0;
}

.tab-pane .tab-content-area {
    -fx-background-color: #1e1e1e;
    -fx-padding: 10px;
}

/* Sidebar tab pane specific styling */
#sidebarTabPane {
    -fx-background-color: #1e1e1e;
    -fx-border-color: #333333;
    -fx-border-width: 0 0 0 1px;
    -fx-padding: 0;
}

#sidebarTabPane .tab-content-area {
    -fx-padding: 0;
    -fx-background-color: #1e1e1e;
}

/* Responsive sidebar content styling */
.sidebar-content {
    -fx-background-color: #1e1e1e;
    -fx-padding: 5px;
    -fx-spacing: 5px;
}

/* Responsive sidebar for different screen sizes */
@media screen and (max-width: 600px) {
    .sidebar-content {
        -fx-padding: 3px;
        -fx-spacing: 3px;
    }
}

@media screen and (min-width: 1200px) {
    .sidebar-content {
        -fx-padding: 8px;
        -fx-spacing: 8px;
    }
}

.section-header {
    -fx-font-weight: bold;
    -fx-text-fill: white;
    -fx-font-size: 12px; /* Smaller font */
    -fx-padding: 0 0 3px 0; /* Reduced padding */
    -fx-border-color: transparent transparent #333333 transparent;
    -fx-border-width: 0 0 1px 0;
}

/* Playlist buttons styling */
.playlist-button {
    -fx-background-color: #333333;
    -fx-background-radius: 4px;
    -fx-min-width: 24px; /* Smaller buttons */
    -fx-min-height: 24px;
    -fx-max-width: 24px;
    -fx-max-height: 24px;
    -fx-padding: 4px; /* Reduced padding */
}

.playlist-button:hover {
    -fx-background-color: #444444;
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.6), 5, 0, 0, 0);
}

/* Main split pane styling */
.main-split-pane {
    -fx-background-color: #1e1e1e;
    -fx-padding: 0;
}

/* Effects tab pane specific styling */
.effects-tab-pane {
    -fx-background-color: #1e1e1e;
    -fx-border-color: #333333;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-padding: 0;
    -fx-pref-height: 150px; /* Reduced height */
    -fx-max-height: 200px;  /* Limit maximum height */
}

.effects-tab-pane .tab-header-area {
    -fx-padding: 2px 2px 0 2px; /* Reduced padding */
}

.effects-tab-pane .tab-content-area {
    -fx-padding: 5px; /* Reduced padding */
    -fx-background-color: #252525;
    -fx-background-radius: 0 0 4px 4px;
}

/* Enhanced list view styling */
.list-view {
    -fx-background-color: #1e1e1e;
    -fx-control-inner-background: #1e1e1e;
    -fx-border-color: #333333;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-padding: 3px;
    -fx-background-radius: 6px;
    -fx-fixed-cell-size: 32px;
    -fx-effect: innershadow(gaussian, rgba(0, 0, 0, 0.2), 5, 0, 0, 0);
}

.list-cell {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-padding: 6px 12px;
    -fx-border-color: transparent transparent rgba(255, 255, 255, 0.1) transparent;
    -fx-border-width: 0 0 1px 0;
    -fx-background-radius: 4px;
    -fx-font-size: 1em;
}

.list-cell:last-visible {
    -fx-border-width: 0;
}

.list-cell:filled:selected {
    -fx-background-color: linear-gradient(to right, #1DB954, #1ed760);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(29, 185, 84, 0.4), 5, 0, 0, 0);
}

.list-cell:filled:hover {
    -fx-background-color: rgba(255, 255, 255, 0.08);
    -fx-background-radius: 4px;
}

.list-cell:filled:selected:hover {
    -fx-background-color: linear-gradient(to right, #1ed760, #22e068);
    -fx-effect: dropshadow(gaussian, rgba(29, 185, 84, 0.6), 8, 0, 0, 0);
}

/* Playlist view specific styling */
#playlistView, #libraryView, #playlistsListView {
    -fx-background-color: #252525;
    -fx-control-inner-background: #252525;
}

#playlistView .list-cell, #libraryView .list-cell, #playlistsListView .list-cell {
    -fx-background-color: #252525;
}

#playlistView .list-cell:filled:hover, #libraryView .list-cell:filled:hover, #playlistsListView .list-cell:filled:hover {
    -fx-background-color: #333333;
}

/* Split pane styling */
.split-pane {
    -fx-background-color: #1e1e1e;
}

.split-pane-divider {
    -fx-background-color: #333333;
    -fx-padding: 1px;
}

/* Menu styling */
.menu-bar {
    -fx-background-color: #252525;
    -fx-border-color: #333333;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 2px;
}

.menu {
    -fx-background-color: transparent;
    -fx-padding: 4px 8px;
}

.menu > .label {
    -fx-text-fill: white;
}

.menu:hover {
    -fx-background-color: #444444;
}

.menu:showing {
    -fx-background-color: #1DB954;
}

.menu-item {
    -fx-background-color: #333333;
    -fx-padding: 8px 16px;
}

.menu-item > .label {
    -fx-text-fill: white;
}

.menu-item:hover {
    -fx-background-color: #1DB954;
}

.menu-item:focused {
    -fx-background-color: #1DB954;
}

.context-menu {
    -fx-background-color: #333333;
    -fx-border-color: #444444;
    -fx-border-width: 1px;
    -fx-padding: 2px;
}

/* Separator styling */
.separator:horizontal .line {
    -fx-border-color: #444444;
    -fx-border-width: 1px;
}

.separator:vertical .line {
    -fx-border-color: #444444;
    -fx-border-width: 1px;
}

/* Scroll bar styling */
.scroll-bar {
    -fx-background-color: transparent;
    -fx-pref-width: 12px;
    -fx-pref-height: 12px;
    -fx-padding: 2px;
}

.scroll-bar .thumb {
    -fx-background-color: #444444;
    -fx-background-radius: 6px;
    -fx-background-insets: 2px;
}

.scroll-bar .thumb:hover {
    -fx-background-color: #555555;
}

.scroll-bar .thumb:pressed {
    -fx-background-color: #1DB954;
}

.scroll-bar .increment-button,
.scroll-bar .decrement-button {
    -fx-background-color: transparent;
    -fx-padding: 0 4px 0 4px;
}

.scroll-bar .increment-arrow,
.scroll-bar .decrement-arrow {
    -fx-background-color: #777777;
    -fx-shape: " ";
    -fx-padding: 0;
}

.scroll-bar:horizontal .track,
.scroll-bar:vertical .track {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-background-radius: 0;
}

.scroll-pane {
    -fx-background-color: transparent;
    -fx-padding: 0;
    -fx-background-insets: 0;
}

.scroll-pane > .viewport {
    -fx-background-color: transparent;
}

/* Equalizer component styling */
.equalizer-component {
    -fx-background-color: #252525;
    -fx-padding: 5px; /* Reduced padding */
    -fx-spacing: 5px; /* Reduced spacing */
    -fx-alignment: center;
    -fx-border-radius: 4px;
}

.equalizer-component .slider {
    -fx-orientation: vertical;
    -fx-pref-height: 100px; /* Reduced height */
    -fx-max-height: 120px;  /* Limit maximum height */
    -fx-min-width: 20px;    /* Narrower sliders */
    -fx-max-width: 25px;    /* Limit maximum width */
    -fx-show-tick-marks: true;
    -fx-show-tick-labels: false;
    -fx-major-tick-unit: 0.25;
}

.equalizer-component .slider .thumb {
    -fx-background-color: #1DB954;
}

.equalizer-component .label {
    -fx-text-fill: white;
    -fx-font-size: 10px;
    -fx-alignment: center;
}

/* Visualization component styling */
.visualization-component {
    -fx-background-color: #121212;
    -fx-border-color: #333333;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-padding: 10px;
    -fx-alignment: center;
}

.visualization-component .combo-box {
    -fx-background-color: #333333;
    -fx-text-fill: white;
    -fx-mark-color: white;
}

.visualization-component .combo-box .list-cell {
    -fx-text-fill: white;
    -fx-background-color: #333333;
}

.visualization-component .combo-box .list-view {
    -fx-background-color: #333333;
    -fx-border-color: #444444;
}

/* Subtitle component styling */
.subtitle-component {
    -fx-background-color: rgba(0, 0, 0, 0.7);
    -fx-text-fill: white;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-padding: 15px;
    -fx-background-radius: 5px;
    -fx-alignment: center;
    -fx-text-alignment: center;
    -fx-wrap-text: true;
    -fx-effect: dropshadow(gaussian, black, 10, 0, 0, 0);
}

/* Sound effects component styling */
.sound-effects-component {
    -fx-background-color: #252525;
    -fx-padding: 15px;
    -fx-spacing: 10px;
    -fx-alignment: center;
    -fx-border-radius: 4px;
}

.sound-effects-component .button {
    -fx-background-color: #333333;
    -fx-text-fill: white;
    -fx-padding: 8px 15px;
    -fx-background-radius: 4px;
}

.sound-effects-component .button:hover {
    -fx-background-color: #1DB954;
}

/* Enhanced controls container styling */
.controls-container {
    -fx-background-color: linear-gradient(to bottom, #1e1e1e, #181818);
    -fx-border-color: #333333;
    -fx-border-width: 1px 0 0 0;
    -fx-padding: 8px 15px 12px 15px;
    -fx-spacing: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 5, 0, 0, -2);
}

.time-slider-container {
    -fx-padding: 5px 0;
    -fx-alignment: center;
    -fx-background-color: rgba(255, 255, 255, 0.02);
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
}

/* Responsive controls for different screen sizes */
@media screen and (max-width: 600px) {
    .controls-container {
        -fx-padding: 5px 8px 8px 8px;
        -fx-spacing: 5px;
    }

    .time-slider-container {
        -fx-padding: 5px 8px;
    }
}

@media screen and (min-width: 1200px) {
    .controls-container {
        -fx-padding: 12px 20px 15px 20px;
        -fx-spacing: 12px;
    }

    .time-slider-container {
        -fx-padding: 8px 15px;
    }
}

/* Text field styling */
.text-field {
    -fx-background-color: #333333;
    -fx-text-fill: white;
    -fx-prompt-text-fill: #888888;
    -fx-highlight-fill: #1DB954;
    -fx-highlight-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 8px;
    -fx-font-size: 12px;
    -fx-border-color: #444444;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
}

.text-field:focused {
    -fx-border-color: #1DB954;
    -fx-effect: dropshadow(three-pass-box, rgba(29, 185, 84, 0.4), 5, 0, 0, 0);
}

/* Combo box styling */
.combo-box {
    -fx-background-color: #333333;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-border-color: #444444;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-padding: 2px;
}

.combo-box .list-cell {
    -fx-text-fill: white;
    -fx-background-color: #333333;
    -fx-padding: 5px 10px;
}

.combo-box .list-view {
    -fx-background-color: #333333;
    -fx-border-color: #444444;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(three-pass-box, rgba(0, 0, 0, 0.5), 10, 0, 0, 0);
}

.combo-box .arrow {
    -fx-background-color: white;
}

.combo-box:hover {
    -fx-border-color: #1DB954;
}

/* Tooltip styling */
.tooltip {
    -fx-background-color: #333333;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 8px 12px;
    -fx-background-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.5), 10, 0, 0, 0);
}
