@echo off
REM Build script for FPlayer Windows installer

echo Building FPlayer Windows installer...

REM Set paths
set WIX_PATH=C:\Program Files (x86)\WiX Toolset v3.11\bin
set PROJECT_ROOT=..\..
set OUTPUT_DIR=%PROJECT_ROOT%\target\installer
set RESOURCES_DIR=%PROJECT_ROOT%\src\main\resources\com\dev\fplayer\assets

REM Create output directory
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Set variables for WiX
set APP_VERSION=1.0.0
set APP_EXE=%PROJECT_ROOT%\target\FPlayer-%APP_VERSION%.exe
set APP_ICON=%RESOURCES_DIR%\app-icon.ico
set LICENSE_RTF=License.rtf
set BANNER_BMP=banner.bmp
set DIALOG_BMP=dialog.bmp

REM Check if jpackage has been run
if not exist "%APP_EXE%" (
    echo ERROR: Application executable not found at %APP_EXE%
    echo Please run 'mvn javafx:jlink jpackage:jpackage' first
    exit /b 1
)

REM Run WiX tools
echo Compiling WiX source files...
"%WIX_PATH%\candle.exe" FPlayer.wxs -ext WixUIExtension ^
    -dFPlayerExe="%APP_EXE%" ^
    -dAppIcon="%APP_ICON%" ^
    -dLicenseRtf="%LICENSE_RTF%" ^
    -dBannerBmp="%BANNER_BMP%" ^
    -dDialogBmp="%DIALOG_BMP%" ^
    -out "%OUTPUT_DIR%\FPlayer.wixobj"

echo Linking WiX objects...
"%WIX_PATH%\light.exe" -ext WixUIExtension ^
    -out "%OUTPUT_DIR%\FPlayer-%APP_VERSION%.msi" ^
    "%OUTPUT_DIR%\FPlayer.wixobj"

if %ERRORLEVEL% EQU 0 (
    echo Installer created successfully: %OUTPUT_DIR%\FPlayer-%APP_VERSION%.msi
) else (
    echo Error creating installer
    exit /b 1
)

exit /b 0
