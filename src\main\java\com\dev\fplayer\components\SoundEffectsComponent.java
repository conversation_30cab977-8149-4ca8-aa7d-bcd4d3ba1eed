package com.dev.fplayer.components;

import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Label;
import javafx.scene.control.Slider;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.scene.media.MediaPlayer;

/**
 * Component for audio effects like reverb, echo, etc.
 * Note: JavaFX MediaPlayer has limited built-in audio effects,
 * so this is a simplified implementation.
 */
public class SoundEffectsComponent extends VBox {
    
    private MediaPlayer mediaPlayer;
    
    // Effect properties
    private final DoubleProperty balanceProperty = new SimpleDoubleProperty(0);
    private final DoubleProperty rateProperty = new SimpleDoubleProperty(1.0);
    private final DoubleProperty volumeProperty = new SimpleDoubleProperty(1.0);
    
    // Effect controls
    private Slider balanceSlider;
    private Slider rateSlider;
    private Slider volumeSlider;
    private CheckBox muteCheckBox;
    
    /**
     * Create a new sound effects component
     */
    public SoundEffectsComponent() {
        setSpacing(15);
        setPadding(new Insets(10));
        setAlignment(Pos.CENTER);
        
        Label titleLabel = new Label("Sound Effects");
        titleLabel.getStyleClass().add("effects-title");
        
        GridPane controlsGrid = new GridPane();
        controlsGrid.setHgap(10);
        controlsGrid.setVgap(15);
        controlsGrid.setAlignment(Pos.CENTER);
        
        // Balance control
        Label balanceLabel = new Label("Balance:");
        balanceSlider = new Slider(-1.0, 1.0, 0);
        balanceSlider.valueProperty().bindBidirectional(balanceProperty);
        Label balanceValueLabel = new Label("Center");
        
        balanceSlider.valueProperty().addListener((obs, oldVal, newVal) -> {
            double value = newVal.doubleValue();
            if (value < -0.05) {
                balanceValueLabel.setText(String.format("%.0f%% Left", -value * 100));
            } else if (value > 0.05) {
                balanceValueLabel.setText(String.format("%.0f%% Right", value * 100));
            } else {
                balanceValueLabel.setText("Center");
            }
        });
        
        // Playback rate control
        Label rateLabel = new Label("Speed:");
        rateSlider = new Slider(0.5, 2.0, 1.0);
        rateSlider.valueProperty().bindBidirectional(rateProperty);
        Label rateValueLabel = new Label("1.0x");
        
        rateSlider.valueProperty().addListener((obs, oldVal, newVal) -> {
            rateValueLabel.setText(String.format("%.2fx", newVal.doubleValue()));
        });
        
        // Volume control
        Label volumeLabel = new Label("Volume:");
        volumeSlider = new Slider(0, 1.0, 1.0);
        volumeSlider.valueProperty().bindBidirectional(volumeProperty);
        Label volumeValueLabel = new Label("100%");
        
        volumeSlider.valueProperty().addListener((obs, oldVal, newVal) -> {
            volumeValueLabel.setText(String.format("%.0f%%", newVal.doubleValue() * 100));
        });
        
        // Mute control
        muteCheckBox = new CheckBox("Mute");
        
        // Add controls to grid
        controlsGrid.add(balanceLabel, 0, 0);
        controlsGrid.add(balanceSlider, 1, 0);
        controlsGrid.add(balanceValueLabel, 2, 0);
        
        controlsGrid.add(rateLabel, 0, 1);
        controlsGrid.add(rateSlider, 1, 1);
        controlsGrid.add(rateValueLabel, 2, 1);
        
        controlsGrid.add(volumeLabel, 0, 2);
        controlsGrid.add(volumeSlider, 1, 2);
        controlsGrid.add(volumeValueLabel, 2, 2);
        
        controlsGrid.add(muteCheckBox, 1, 3);
        
        // Effect preset buttons
        GridPane presetGrid = new GridPane();
        presetGrid.setHgap(10);
        presetGrid.setVgap(10);
        presetGrid.setAlignment(Pos.CENTER);
        
        presetGrid.add(createEffectButton("Normal", 0, 1.0, 1.0, false), 0, 0);
        presetGrid.add(createEffectButton("Slow", 0, 0.75, 1.0, false), 1, 0);
        presetGrid.add(createEffectButton("Fast", 0, 1.5, 1.0, false), 2, 0);
        presetGrid.add(createEffectButton("Left Channel", -1.0, 1.0, 1.0, false), 0, 1);
        presetGrid.add(createEffectButton("Right Channel", 1.0, 1.0, 1.0, false), 1, 1);
        presetGrid.add(createEffectButton("Low Volume", 0, 1.0, 0.3, false), 2, 1);
        
        getChildren().addAll(titleLabel, controlsGrid, presetGrid);
    }
    
    /**
     * Create an effect preset button
     * @param name The name of the effect
     * @param balance The balance value
     * @param rate The playback rate
     * @param volume The volume level
     * @param mute Whether to mute
     * @return The effect button
     */
    private javafx.scene.control.Button createEffectButton(String name, double balance, double rate, double volume, boolean mute) {
        javafx.scene.control.Button button = new javafx.scene.control.Button(name);
        button.getStyleClass().add("effect-button");
        
        button.setOnAction(e -> {
            balanceProperty.set(balance);
            rateProperty.set(rate);
            volumeProperty.set(volume);
            muteCheckBox.setSelected(mute);
            
            if (mediaPlayer != null) {
                mediaPlayer.setMute(mute);
            }
        });
        
        return button;
    }
    
    /**
     * Connect the effects to a media player
     * @param mediaPlayer The media player to connect to
     */
    public void connectToMediaPlayer(MediaPlayer mediaPlayer) {
        this.mediaPlayer = mediaPlayer;
        
        if (mediaPlayer != null) {
            // Bind properties to media player
            mediaPlayer.balanceProperty().bind(balanceProperty);
            mediaPlayer.rateProperty().bind(rateProperty);
            mediaPlayer.volumeProperty().bind(volumeProperty);
            
            // Bind mute checkbox
            muteCheckBox.selectedProperty().addListener((obs, oldVal, newVal) -> {
                mediaPlayer.setMute(newVal);
            });
            
            // Set initial mute state
            mediaPlayer.setMute(muteCheckBox.isSelected());
        }
    }
    
    /**
     * Reset all effects to default values
     */
    public void reset() {
        balanceProperty.set(0);
        rateProperty.set(1.0);
        volumeProperty.set(1.0);
        muteCheckBox.setSelected(false);
        
        if (mediaPlayer != null) {
            mediaPlayer.setMute(false);
        }
    }
    
    // Getters for properties
    
    public DoubleProperty balanceProperty() {
        return balanceProperty;
    }
    
    public DoubleProperty rateProperty() {
        return rateProperty;
    }
    
    public DoubleProperty volumeProperty() {
        return volumeProperty;
    }
}
