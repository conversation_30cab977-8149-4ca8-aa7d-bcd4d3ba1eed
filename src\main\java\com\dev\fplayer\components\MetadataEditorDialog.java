package com.dev.fplayer.components;

import com.dev.fplayer.models.MediaItem;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * Dialog for editing media metadata
 */
public class MetadataEditorDialog extends Dialog<Map<String, Object>> {

    private MediaItem mediaItem;
    private TextField titleField;
    private TextField artistField;
    private TextField albumField;
    private TextField genreField;
    private TextField yearField;
    private TextArea commentsArea;
    private ImageView albumArtView;
    private File albumArtFile;
    private Map<String, TextField> customFields;

    /**
     * Create a new metadata editor dialog
     *
     * @param mediaItem The media item to edit
     */
    public MetadataEditorDialog(MediaItem mediaItem) {
        this.mediaItem = mediaItem;
        this.customFields = new HashMap<>();

        // Set up dialog
        setTitle("Edit Metadata");
        setHeaderText("Edit metadata for: " + mediaItem.getTitle());

        // Set the button types
        ButtonType saveButtonType = new ButtonType("Save", ButtonBar.ButtonData.OK_DONE);
        getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);

        // Create the form grid
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        // Create form fields
        titleField = new TextField(mediaItem.getTitle());
        titleField.setPromptText("Title");

        artistField = new TextField(mediaItem.getArtist());
        artistField.setPromptText("Artist");

        albumField = new TextField(mediaItem.getAlbum());
        albumField.setPromptText("Album");

        genreField = new TextField(mediaItem.getGenre());
        genreField.setPromptText("Genre");

        yearField = new TextField(mediaItem.getYear() > 0 ? String.valueOf(mediaItem.getYear()) : "");
        yearField.setPromptText("Year");

        commentsArea = new TextArea();
        commentsArea.setPromptText("Comments");
        if (mediaItem.getMetadata().containsKey("comments")) {
            commentsArea.setText((String) mediaItem.getMetadata().get("comments"));
        }

        // Album art
        albumArtView = new ImageView();
        albumArtView.setFitHeight(150);
        albumArtView.setFitWidth(150);
        albumArtView.setPreserveRatio(true);

        // Load album art if available
        if (mediaItem.getMetadata().containsKey("albumArt")) {
            Object art = mediaItem.getMetadata().get("albumArt");
            if (art instanceof Image) {
                albumArtView.setImage((Image) art);
            } else if (art instanceof File) {
                albumArtFile = (File) art;
                try {
                    albumArtView.setImage(new Image(albumArtFile.toURI().toString()));
                } catch (Exception e) {
                    // Ignore if image can't be loaded
                }
            }
        }

        // Add fields to grid
        grid.add(new Label("Title:"), 0, 0);
        grid.add(titleField, 1, 0);

        grid.add(new Label("Artist:"), 0, 1);
        grid.add(artistField, 1, 1);

        grid.add(new Label("Album:"), 0, 2);
        grid.add(albumField, 1, 2);

        grid.add(new Label("Genre:"), 0, 3);
        grid.add(genreField, 1, 3);

        grid.add(new Label("Year:"), 0, 4);
        grid.add(yearField, 1, 4);

        grid.add(new Label("Comments:"), 0, 5);
        grid.add(commentsArea, 1, 5);

        // Album art buttons
        Button browseButton = new Button("Browse...");
        browseButton.setOnAction(e -> {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Select Album Art");
            fileChooser.getExtensionFilters().addAll(
                    new FileChooser.ExtensionFilter("Image Files", "*.png", "*.jpg", "*.jpeg", "*.gif", "*.bmp"));

            File file = fileChooser.showOpenDialog(getDialogPane().getScene().getWindow());
            if (file != null) {
                albumArtFile = file;
                try {
                    albumArtView.setImage(new Image(file.toURI().toString()));
                } catch (Exception ex) {
                    // Show error if image can't be loaded
                    Alert alert = new Alert(Alert.AlertType.ERROR);
                    alert.setTitle("Error");
                    alert.setHeaderText("Could not load image");
                    alert.setContentText("The selected file could not be loaded as an image.");
                    alert.showAndWait();
                }
            }
        });

        Button clearButton = new Button("Clear");
        clearButton.setOnAction(e -> {
            albumArtFile = null;
            albumArtView.setImage(null);
        });

        HBox albumArtButtons = new HBox(10, browseButton, clearButton);
        VBox albumArtBox = new VBox(10, albumArtView, albumArtButtons);

        grid.add(new Label("Album Art:"), 0, 6);
        grid.add(albumArtBox, 1, 6);

        // Add custom metadata fields
        final int[] rowRef = { 7 }; // Use array to hold mutable int reference

        for (Map.Entry<String, Object> entry : mediaItem.getMetadata().entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // Skip comments and albumArt as they're already handled
            if (key.equals("comments") || key.equals("albumArt")) {
                continue;
            }

            // Add custom field
            Label label = new Label(key + ":");
            TextField field = new TextField(value != null ? value.toString() : "");

            grid.add(label, 0, rowRef[0]);
            grid.add(field, 1, rowRef[0]);

            customFields.put(key, field);
            rowRef[0]++;
        }

        // Add button to add custom field
        Button addCustomButton = new Button("Add Custom Field");
        addCustomButton.setOnAction(e -> {
            TextInputDialog dialog = new TextInputDialog();
            dialog.setTitle("Add Custom Field");
            dialog.setHeaderText("Enter the name of the custom field");
            dialog.setContentText("Field name:");

            dialog.showAndWait().ifPresent(fieldName -> {
                if (!fieldName.isEmpty() && !customFields.containsKey(fieldName)) {
                    Label label = new Label(fieldName + ":");
                    TextField field = new TextField();

                    grid.add(label, 0, rowRef[0]);
                    grid.add(field, 1, rowRef[0]);

                    customFields.put(fieldName, field);
                    rowRef[0]++;

                    // Resize dialog to fit new field
                    Stage stage = (Stage) getDialogPane().getScene().getWindow();
                    stage.sizeToScene();
                }
            });
        });

        grid.add(addCustomButton, 1, rowRef[0]);

        // Set the dialog content
        getDialogPane().setContent(grid);

        // Request focus on the title field by default
        titleField.requestFocus();

        // Convert the result to a metadata map when the save button is clicked
        setResultConverter(dialogButton -> {
            if (dialogButton == saveButtonType) {
                return getMetadataFromFields();
            }
            return null;
        });
    }

    /**
     * Get metadata from form fields
     *
     * @return Map of metadata values
     */
    private Map<String, Object> getMetadataFromFields() {
        Map<String, Object> metadata = new HashMap<>();

        // Add standard fields
        metadata.put("title", titleField.getText());
        metadata.put("artist", artistField.getText());
        metadata.put("album", albumField.getText());
        metadata.put("genre", genreField.getText());

        // Parse year as integer
        try {
            if (!yearField.getText().isEmpty()) {
                metadata.put("year", Integer.parseInt(yearField.getText()));
            }
        } catch (NumberFormatException e) {
            // Ignore invalid year
        }

        // Add comments
        if (!commentsArea.getText().isEmpty()) {
            metadata.put("comments", commentsArea.getText());
        }

        // Add album art
        if (albumArtFile != null) {
            metadata.put("albumArt", albumArtFile);
        } else if (albumArtView.getImage() != null) {
            metadata.put("albumArt", albumArtView.getImage());
        }

        // Add custom fields
        for (Map.Entry<String, TextField> entry : customFields.entrySet()) {
            String key = entry.getKey();
            TextField field = entry.getValue();

            if (!field.getText().isEmpty()) {
                metadata.put(key, field.getText());
            }
        }

        return metadata;
    }

    /**
     * Apply metadata to the media item
     *
     * @param metadata The metadata to apply
     */
    public void applyMetadata(Map<String, Object> metadata) {
        if (metadata == null) {
            return;
        }

        // Apply standard fields
        if (metadata.containsKey("title")) {
            mediaItem.setTitle((String) metadata.get("title"));
        }

        if (metadata.containsKey("artist")) {
            mediaItem.setArtist((String) metadata.get("artist"));
        }

        if (metadata.containsKey("album")) {
            mediaItem.setAlbum((String) metadata.get("album"));
        }

        if (metadata.containsKey("genre")) {
            mediaItem.setGenre((String) metadata.get("genre"));
        }

        if (metadata.containsKey("year")) {
            if (metadata.get("year") instanceof Integer) {
                mediaItem.setYear((Integer) metadata.get("year"));
            } else if (metadata.get("year") instanceof String) {
                try {
                    mediaItem.setYear(Integer.parseInt((String) metadata.get("year")));
                } catch (NumberFormatException e) {
                    // Ignore invalid year
                }
            }
        }

        // Apply all metadata to the metadata map
        for (Map.Entry<String, Object> entry : metadata.entrySet()) {
            mediaItem.addMetadata(entry.getKey(), entry.getValue());
        }
    }
}
