package com.dev.fplayer.models;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a playlist of media items
 */
public class Playlist implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String name;
    private transient ObservableList<MediaItem> items;
    private List<String> filePaths; // For serialization
    
    /**
     * Create a new empty playlist
     * @param name The name of the playlist
     */
    public Playlist(String name) {
        this.name = name;
        this.items = FXCollections.observableArrayList();
        this.filePaths = new ArrayList<>();
    }
    
    /**
     * Add a media item to the playlist
     * @param item The media item to add
     */
    public void addItem(MediaItem item) {
        items.add(item);
        if (item.getFile() != null) {
            filePaths.add(item.getFile().getAbsolutePath());
        } else if (item.getUrl() != null) {
            filePaths.add(item.getUrl());
        }
    }
    
    /**
     * Remove a media item from the playlist
     * @param item The media item to remove
     */
    public void removeItem(MediaItem item) {
        items.remove(item);
        if (item.getFile() != null) {
            filePaths.remove(item.getFile().getAbsolutePath());
        } else if (item.getUrl() != null) {
            filePaths.remove(item.getUrl());
        }
    }
    
    /**
     * Move an item up in the playlist
     * @param index The index of the item to move
     */
    public void moveItemUp(int index) {
        if (index > 0 && index < items.size()) {
            MediaItem item = items.get(index);
            items.remove(index);
            items.add(index - 1, item);
            
            // Update filePaths to match
            String path = filePaths.get(index);
            filePaths.remove(index);
            filePaths.add(index - 1, path);
        }
    }
    
    /**
     * Move an item down in the playlist
     * @param index The index of the item to move
     */
    public void moveItemDown(int index) {
        if (index >= 0 && index < items.size() - 1) {
            MediaItem item = items.get(index);
            items.remove(index);
            items.add(index + 1, item);
            
            // Update filePaths to match
            String path = filePaths.get(index);
            filePaths.remove(index);
            filePaths.add(index + 1, path);
        }
    }
    
    /**
     * Clear all items from the playlist
     */
    public void clear() {
        items.clear();
        filePaths.clear();
    }
    
    /**
     * Get the number of items in the playlist
     * @return The number of items
     */
    public int size() {
        return items.size();
    }
    
    /**
     * Check if the playlist is empty
     * @return True if the playlist is empty, false otherwise
     */
    public boolean isEmpty() {
        return items.isEmpty();
    }
    
    /**
     * Get the observable list of media items
     * @return The observable list of media items
     */
    public ObservableList<MediaItem> getItems() {
        return items;
    }
    
    /**
     * Get the name of the playlist
     * @return The name of the playlist
     */
    public String getName() {
        return name;
    }
    
    /**
     * Set the name of the playlist
     * @param name The new name of the playlist
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * Get the list of file paths (for serialization)
     * @return The list of file paths
     */
    public List<String> getFilePaths() {
        return filePaths;
    }
    
    /**
     * Set the list of file paths and rebuild the items list
     * @param filePaths The list of file paths
     */
    public void setFilePaths(List<String> filePaths) {
        this.filePaths = filePaths;
        rebuildItemsFromPaths();
    }
    
    /**
     * Rebuild the items list from the file paths
     */
    private void rebuildItemsFromPaths() {
        items.clear();
        for (String path : filePaths) {
            if (path.startsWith("http://") || path.startsWith("https://")) {
                // URL
                MediaItem item = new MediaItem(path, new File(path).getName(), MediaItem.MediaType.UNKNOWN);
                items.add(item);
            } else {
                // File
                File file = new File(path);
                if (file.exists()) {
                    MediaItem item = new MediaItem(file);
                    items.add(item);
                }
            }
        }
    }
    
    @Override
    public String toString() {
        return name + " (" + items.size() + " items)";
    }
}
