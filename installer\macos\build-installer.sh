#!/bin/bash
# Build script for FPlayer macOS installer

echo "Building FPlayer macOS installer..."

# Set paths
PROJECT_ROOT=../..
OUTPUT_DIR=$PROJECT_ROOT/target/installer
RESOURCES_DIR=$PROJECT_ROOT/src/main/resources/com/dev/fplayer/assets

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Set variables
APP_VERSION=1.0.0
APP_NAME=FPlayer
APP_BUNDLE=$PROJECT_ROOT/target/$APP_NAME-$APP_VERSION.app
APP_ICON=$RESOURCES_DIR/app-icon.icns
INFO_PLIST=Info.plist

# Check if jpackage has been run
if [ ! -d "$APP_BUNDLE" ]; then
    echo "ERROR: Application bundle not found at $APP_BUNDLE"
    echo "Please run 'mvn javafx:jlink jpackage:jpackage' first"
    exit 1
fi

# Create DMG
echo "Creating DMG installer..."
hdiutil create -volname "$APP_NAME" \
               -srcfolder "$APP_BUNDLE" \
               -ov -format UDZO \
               "$OUTPUT_DIR/$APP_NAME-$APP_VERSION.dmg"

if [ $? -eq 0 ]; then
    echo "Installer created successfully: $OUTPUT_DIR/$APP_NAME-$APP_VERSION.dmg"
else
    echo "Error creating installer"
    exit 1
fi

exit 0
