package com.dev.fplayer.components;

import com.dev.fplayer.models.MediaItem;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;

import java.util.HashMap;
import java.util.Map;

/**
 * Dialog for streaming media from URLs
 */
public class StreamingDialog extends Dialog<MediaItem> {
    
    private TextField urlField;
    private TextField titleField;
    private ComboBox<String> typeComboBox;
    private ListView<String> presetListView;
    private Map<String, String> presets;
    
    /**
     * Create a new streaming dialog
     */
    public StreamingDialog() {
        // Initialize presets
        presets = new HashMap<>();
        presets.put("YouTube Music - Lo-Fi Hip Hop", "https://www.youtube.com/watch?v=5qap5aO4i9A");
        presets.put("SomaFM - Groove Salad", "https://somafm.com/groovesalad256.pls");
        presets.put("SomaFM - Drone Zone", "https://somafm.com/dronezone256.pls");
        presets.put("BBC Radio 1", "http://stream.live.vc.bbcmedia.co.uk/bbc_radio_one");
        presets.put("Classical KUSC", "https://playerservices.streamtheworld.com/api/livestream-redirect/KUSCMP128.mp3");
        presets.put("KEXP Seattle", "https://kexp.streamguys1.com/kexp160.aac");
        presets.put("Big Buck Bunny (Video)", "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4");
        presets.put("Tears of Steel (Video)", "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4");
        
        // Set up dialog
        setTitle("Stream Media");
        setHeaderText("Enter a URL to stream media");
        
        // Set the button types
        ButtonType streamButtonType = new ButtonType("Stream", ButtonBar.ButtonData.OK_DONE);
        getDialogPane().getButtonTypes().addAll(streamButtonType, ButtonType.CANCEL);
        
        // Create the form grid
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 10, 10, 10));
        
        // Create form fields
        urlField = new TextField();
        urlField.setPromptText("https://example.com/stream.mp3");
        urlField.setPrefWidth(400);
        
        titleField = new TextField();
        titleField.setPromptText("Stream Title");
        
        typeComboBox = new ComboBox<>();
        typeComboBox.getItems().addAll("Audio", "Video");
        typeComboBox.setValue("Audio");
        
        // Create presets list
        presetListView = new ListView<>();
        ObservableList<String> presetNames = FXCollections.observableArrayList(presets.keySet());
        presetListView.setItems(presetNames);
        presetListView.setPrefHeight(200);
        
        // Add selection listener to presets
        presetListView.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                urlField.setText(presets.get(newVal));
                titleField.setText(newVal);
                
                // Set type based on URL
                if (newVal.contains("Video")) {
                    typeComboBox.setValue("Video");
                } else {
                    typeComboBox.setValue("Audio");
                }
            }
        });
        
        // Create add preset button
        Button addPresetButton = new Button("Add to Presets");
        addPresetButton.setOnAction(e -> {
            String url = urlField.getText();
            String title = titleField.getText();
            
            if (!url.isEmpty() && !title.isEmpty()) {
                presets.put(title, url);
                presetListView.getItems().add(title);
            } else {
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("Error");
                alert.setHeaderText("Invalid Input");
                alert.setContentText("Please enter both a title and URL.");
                alert.showAndWait();
            }
        });
        
        // Create remove preset button
        Button removePresetButton = new Button("Remove Preset");
        removePresetButton.setOnAction(e -> {
            String selected = presetListView.getSelectionModel().getSelectedItem();
            if (selected != null) {
                presets.remove(selected);
                presetListView.getItems().remove(selected);
            }
        });
        
        // Create button box
        HBox buttonBox = new HBox(10);
        buttonBox.getChildren().addAll(addPresetButton, removePresetButton);
        
        // Create presets box
        VBox presetsBox = new VBox(10);
        presetsBox.getChildren().addAll(new Label("Presets:"), presetListView, buttonBox);
        
        // Add fields to grid
        grid.add(new Label("URL:"), 0, 0);
        grid.add(urlField, 1, 0);
        
        grid.add(new Label("Title:"), 0, 1);
        grid.add(titleField, 1, 1);
        
        grid.add(new Label("Type:"), 0, 2);
        grid.add(typeComboBox, 1, 2);
        
        grid.add(presetsBox, 0, 3, 2, 1);
        
        // Set the dialog content
        getDialogPane().setContent(grid);
        
        // Request focus on the URL field by default
        urlField.requestFocus();
        
        // Convert the result to a media item when the stream button is clicked
        setResultConverter(dialogButton -> {
            if (dialogButton == streamButtonType) {
                String url = urlField.getText();
                String title = titleField.getText();
                String type = typeComboBox.getValue();
                
                if (url.isEmpty()) {
                    return null;
                }
                
                if (title.isEmpty()) {
                    title = url;
                }
                
                MediaItem.MediaType mediaType = type.equals("Video") ? 
                    MediaItem.MediaType.VIDEO : MediaItem.MediaType.AUDIO;
                
                return new MediaItem(url, title, mediaType);
            }
            return null;
        });
    }
}
